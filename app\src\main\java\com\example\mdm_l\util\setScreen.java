package com.example.mdm_l.util;

import android.app.Activity;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.PowerManager;
import android.view.WindowManager;

/**
 * 工具类：控制屏幕的开和关（息屏/亮屏）
 * 注意：需要设备所有者权限或系统权限
 */
public class setScreen {

    private PowerManager.WakeLock wakeLock;

    /**
     * 点亮屏幕
     * @param activity 当前前台 Activity，用于添加窗口标志
     */
    public void turnOnScreen(Activity activity) {
        PowerManager powerManager = (PowerManager) activity.getSystemService(Context.POWER_SERVICE);
        if (powerManager != null && !powerManager.isInteractive()) {
            wakeLock = powerManager.newWakeLock(
                    PowerManager.SCREEN_BRIGHT_WAKE_LOCK |
                            PowerManager.ACQUIRE_CAUSES_WAKEUP,
                    "MDM:ScreenOnWakeLock"
            );
            wakeLock.acquire(10 * 1000L); // 最多亮屏10秒
        }

        // 设置窗口标志以唤醒并显示
        activity.getWindow().addFlags(
                WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON |
                        WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                        WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON
        );
    }

    /**
     * 释放亮屏的 WakeLock（可选调用）
     */
    public void releaseWakeLock() {
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
            wakeLock = null;
        }
    }

    /**
     * 熄灭/锁定屏幕（需要设备所有者权限）
     * @param context 上下文
     * @param adminComponent 你的 DeviceAdminReceiver 组件
     */
    public void turnOffScreen(Context context, ComponentName adminComponent) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        if (dpm != null && dpm.isAdminActive(adminComponent)) {
            dpm.lockNow(); // 锁屏并息屏
        }
    }
}
