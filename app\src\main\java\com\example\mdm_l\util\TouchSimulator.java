package com.example.mdm_l.util;

import android.util.Log;

import java.io.IOException;

public class TouchSimulator {
    public static void simulateTap(int x, int y) {
        try {
            String command = "input tap " + x + " " + y;
            Runtime.getRuntime().exec(new String[]{"sh", "-c", command});
            Log.d("TouchSimulator", "执行命令: " + command);
        } catch (IOException e) {
            Log.e("TouchSimulator", "模拟点击失败: " + e.getMessage());
        }
    }
}
