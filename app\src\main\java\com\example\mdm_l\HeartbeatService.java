package com.example.mdm_l;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import com.example.mdm_l.R;
import com.example.mdm_l.util.heartbest;

public class HeartbeatService extends Service {
    private static final String TAG = "HeartbeatService";
    private static final String CHANNEL_ID = "heartbeat_channel";
    private static boolean isRunning = false;
    private static heartbest heartbestInstance;

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        Notification notification = new Notification.Builder(this, CHANNEL_ID)
                .setContentTitle("设备管理心跳")
                .setContentText("正在保持连接中...")
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                .build();
        startForeground(1, notification);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (isRunning) {
            Log.i(TAG, "心跳服务已运行，跳过重复启动");
            return START_STICKY;
        }
//        String ip = intent.getStringExtra("ip");

//        int port = intent.getIntExtra("port", 9902);
//        String username = intent.getStringExtra("username");
//        String password = intent.getStringExtra("password");
//        String mac = intent.getStringExtra("mac");
//        String sn = intent.getStringExtra("sn");

//        String ip = "***********";
//        int port = 9902;

//        heartbestInstance = new heartbest(this, ip, port, username, password, mac,sn);
        heartbestInstance = new heartbest(this);
        heartbestInstance.start();
        isRunning = true;
        Log.i(TAG, "心跳服务已启动");
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
//        if (heartbestInstance != null) {
//            heartbestInstance.stop();
//            heartbestInstance = null;
//        }
//        isRunning = false;
//        Log.i(TAG, "心跳服务被销毁");
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null; // 本服务不绑定
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "心跳服务",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }
}
