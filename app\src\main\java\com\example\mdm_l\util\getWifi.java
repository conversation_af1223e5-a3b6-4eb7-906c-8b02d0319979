package com.example.mdm_l.util;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;
import android.util.Log;

public class getWifi {
    public static String getWifiNameString(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if (wifiInfo != null) {
            String ssid = wifiInfo.getSSID(); // WiFi名称
            // 去掉首尾引号
            if (ssid != null && ssid.startsWith("\"") && ssid.endsWith("\"")) {
                ssid = ssid.substring(1, ssid.length() - 1);
            }
            return ssid;
        }
        return "未知";
    }
    public static String getWifiRssiString(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if (wifiInfo != null) {
            int rssi = wifiInfo.getRssi(); // 信号强度（RSSI）
            return ""+rssi;
        }
        return "未知";
    }
    public static String getWifiLevelString(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if (wifiInfo != null) {
            int rssi = wifiInfo.getRssi(); // 信号强度（RSSI）
            int level = WifiManager.calculateSignalLevel(rssi, 5); // 转换成0~4级别
            return ""+level;
        }
        return "未知";
    }
    public static String getWifiMacAddress(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        WifiInfo wifiInfo = wifiManager.getConnectionInfo();
        if(wifiInfo != null) {
            return wifiInfo.getBSSID();
        }
        return "未知";
    }
}
