package com.example.mdm_l;

import android.app.Activity;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.content.pm.ServiceInfo;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.example.mdm_l.util.ScreenshotUtil;
import com.example.mdm_l.util.WebSocketHandler;
import com.example.mdm_l.util.webSocketServer;

public class ScreenshotService extends Service {
    public static final String CHANNEL_ID = "screenshot_channel";

    // 静态保存授权信息（由 Activity 设置）
    private static Intent projectionData;
    private static int projectionCode;

    // Activity 授权成功后调用此方法设置授权结果
    public static void setProjectionData(int code, Intent data) {
        projectionCode = code;
        projectionData = data;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 本服务不支持绑定
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        createNotificationChannel();

        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("MDM")
                .setContentText("MDM设备管理平台可远程")
                .setSmallIcon(R.mipmap.logo)
                .build();


        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(1, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION);
        } else {
            startForeground(1, notification);
        }

        int resultCode = intent.getIntExtra("resultCode", Activity.RESULT_CANCELED);
        Intent data = intent.getParcelableExtra("data");

        if (resultCode == Activity.RESULT_OK && data != null) {
            ScreenshotUtil.startProjection(this, resultCode, data); // ✅ 只授权，不截图
        } else {
            Log.e("ScreenshotService", "授权失败或数据缺失");
        }
        return START_STICKY; // 服务保持运行，等待远程调用截图
    }


    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "截图服务通知",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
}
