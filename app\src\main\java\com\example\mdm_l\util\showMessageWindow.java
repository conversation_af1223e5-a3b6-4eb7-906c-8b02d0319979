package com.example.mdm_l.util;

import android.content.Context;
import android.graphics.PixelFormat;
import android.os.Build;
import android.provider.Settings;
import android.util.DisplayMetrics;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.util.Log;

import com.example.mdm_l.R;

public class showMessageWindow {

    private static WindowManager windowManager;
    private static View floatView;

    public static void showFloatingWindow(Context context, String message) {
        if (!Settings.canDrawOverlays(context)) {
            Log.e("showMessageWindow", "未授予悬浮窗权限，无法显示窗口");
            return;
        }

        if (floatView != null) return; // 防止重复显示

        windowManager = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        LayoutInflater inflater = LayoutInflater.from(context);
        floatView = inflater.inflate(R.layout.floating_message, null);

        TextView messageText = floatView.findViewById(R.id.message_text);
        Button okButton = floatView.findViewById(R.id.ok_button);

        messageText.setText(message);

        okButton.setOnClickListener(v -> {
            if (windowManager != null && floatView != null) {
                windowManager.removeView(floatView);
                floatView = null;
            }
        });
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.MATCH_PARENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.O ?
                        WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY :
                        WindowManager.LayoutParams.TYPE_PHONE,
                WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
                PixelFormat.TRANSLUCENT
        );

        // 设置宽度为屏幕宽度减去左右 10dp margin
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        int screenWidth = metrics.widthPixels;
        int margin = dpToPx(context, 10);
        params.width = screenWidth - margin * 2;

        params.gravity = Gravity.CENTER;

        windowManager.addView(floatView, params);
    }

    public static int dpToPx(Context context, int dp) {
        return (int) (dp * context.getResources().getDisplayMetrics().density + 0.5f);
    }

}
