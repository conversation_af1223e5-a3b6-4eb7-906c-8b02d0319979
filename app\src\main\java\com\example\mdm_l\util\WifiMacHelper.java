package com.example.mdm_l.util;

import android.provider.Settings;
import android.content.Context;

public class WifiMacHelper {

    public static void forceUseDeviceMac(Context context) {
        try {
            Settings.Global.putInt(
                    context.getContentResolver(),
                    "wifi_connected_mac_randomization_enabled",
                    0 // 0 表示关闭随机 MAC，使用真实 MAC
            );
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
