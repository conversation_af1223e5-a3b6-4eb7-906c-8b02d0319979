package com.example.mdm_l.util;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.PixelFormat;
import android.hardware.display.DisplayManager;
import android.hardware.display.VirtualDisplay;
import android.media.Image;
import android.media.ImageReader;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.WindowManager;

import androidx.annotation.RequiresApi;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;

public class ScreenshotUtil {

    private static final String TAG = "ScreenshotUtil";
    private static final int REQUEST_CODE = 1001;
    private static boolean isCapturing = false;

    private static MediaProjection mediaProjection;
    private static VirtualDisplay virtualDisplay;
    private static ImageReader imageReader;
    private static boolean isStreaming = false;

    public static void requestScreenshotPermission(Activity activity) {
        if (mediaProjection != null) {
            Log.i("ScreenshotUtil", "已授权，无需再次请求");
            return;
        }
        MediaProjectionManager mpm = (MediaProjectionManager)
                activity.getSystemService(Activity.MEDIA_PROJECTION_SERVICE);
        Intent intent = mpm.createScreenCaptureIntent();
        activity.startActivityForResult(intent, REQUEST_CODE);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public static void onActivityResult(Activity activity, int resultCode, Intent data) {
        if (resultCode != Activity.RESULT_OK) {
            Log.e(TAG, "用户未授权截屏");
            return;
        }

        Intent intent = new Intent(activity, com.example.mdm_l.ScreenshotService.class);
        intent.putExtra("resultCode", resultCode);
        intent.putExtra("data", data);
        activity.startForegroundService(intent);
    }

    public static void startProjection(Context context, int resultCode, Intent data) {
        MediaProjectionManager mpm = (MediaProjectionManager)
                context.getSystemService(Context.MEDIA_PROJECTION_SERVICE);
        if (mpm != null) {
            mediaProjection = mpm.getMediaProjection(resultCode, data);
            if (mediaProjection != null) {
                Log.d(TAG, "MediaProjection 初始化完成");  // 这里不要直接调用截图
            } else {
                Log.e(TAG, "mediaProjection 为 null");
            }
        } else {
            Log.e(TAG, "MediaProjectionManager 获取失败");
        }
    }

    public static boolean isReady() {
        return mediaProjection != null;
    }


    @SuppressLint("WrongConstant")
    public static void captureScreen(Context context) {
        if (mediaProjection == null) {
            Log.e(TAG, "captureScreen: mediaProjection 为 null，请先授权");
            return;
        }

        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(metrics);

        int width = metrics.widthPixels;
        int height = metrics.heightPixels;
        int dpi = metrics.densityDpi;

        imageReader = ImageReader.newInstance(width, height, PixelFormat.RGBA_8888, 2);

        virtualDisplay = mediaProjection.createVirtualDisplay(
                "ScreenCapture",
                width, height, dpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.getSurface(), null, null
        );

        isCapturing = true;
        imageReader.setOnImageAvailableListener(reader -> {
            if (!isCapturing) return;
            isCapturing = false;

            Image image = null;
            OutputStream os = null;
            Bitmap bitmap = null;

            try {
                image = reader.acquireLatestImage();
                if (image != null) {
                    Image.Plane[] planes = image.getPlanes();
                    ByteBuffer buffer = planes[0].getBuffer();
                    int pixelStride = planes[0].getPixelStride();
                    int rowStride = planes[0].getRowStride();
                    int rowPadding = rowStride - pixelStride * width;

                    Bitmap fullBitmap = Bitmap.createBitmap(
                            width + rowPadding / pixelStride,
                            height,
                            Bitmap.Config.ARGB_8888
                    );
                    fullBitmap.copyPixelsFromBuffer(buffer);
                    // ✅ 裁剪掉右边的黑边
                    bitmap = Bitmap.createBitmap(fullBitmap, 0, 0, width, height);
                    fullBitmap.recycle();

                    long timestamp = System.currentTimeMillis();
                    String filename = "screenshot_" + timestamp + ".png";

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        // Android 10+ 保存到 Pictures/Screenshots
                        ContentResolver resolver = context.getContentResolver();
                        ContentValues contentValues = new ContentValues();
                        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, filename);
                        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/png");
                        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_PICTURES + "/Screenshots");

                        Uri imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
                        if (imageUri != null) {
                            os = resolver.openOutputStream(imageUri);
                            bitmap.compress(Bitmap.CompressFormat.PNG, 100, os);
                            os.flush();
                            Log.d(TAG, "截图成功，保存路径: " + imageUri.toString());
                        } else {
                            Log.e(TAG, "插入 MediaStore 失败");
                        }
                    } else {
                        // Android 9 及以下
                        File path = new File(Environment.getExternalStorageDirectory(), "Screenshots");
                        if (!path.exists()) path.mkdirs();

                        File file = new File(path, filename);
                        os = new FileOutputStream(file);
                        bitmap.compress(Bitmap.CompressFormat.PNG, 100, os);
                        os.flush();

                        // 通知图库刷新
                        Intent scanIntent = new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE);
                        scanIntent.setData(Uri.fromFile(file));
                        context.sendBroadcast(scanIntent);

                        Log.d(TAG, "截图成功，保存路径: " + file.getAbsolutePath());
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "截图失败: " + e.getMessage());
            } finally {
                if (image != null) image.close();
                if (bitmap != null) bitmap.recycle();
                try {
                    if (os != null) os.close();
                } catch (Exception ignored) {}

                // ✅ 释放 VirtualDisplay，但保留 MediaProjection 授权
                if (virtualDisplay != null) {
                    virtualDisplay.release();
                    virtualDisplay = null;
                }

                // 解绑监听器
                if (imageReader != null) {
                    imageReader.setOnImageAvailableListener(null, null);
                }
            }
        }, new Handler(Looper.getMainLooper()));
    }

    public static void startContinuousCaptureAndSend(Context context) {
        if (!isReady()) {
            Log.e(TAG, "MediaProjection 未初始化");
            return;
        }

        if (isStreaming) {
            Log.w(TAG, "已经在持续截屏中");
            return;
        }

        WindowManager wm = (WindowManager) context.getSystemService(Context.WINDOW_SERVICE);
        DisplayMetrics metrics = new DisplayMetrics();
        wm.getDefaultDisplay().getRealMetrics(metrics);

        int width = metrics.widthPixels;
        int height = metrics.heightPixels;
        int dpi = metrics.densityDpi;

        imageReader = ImageReader.newInstance(width, height, PixelFormat.RGBA_8888, 2);
        virtualDisplay = mediaProjection.createVirtualDisplay(
                "ScreenCaptureStream",
                width, height, dpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                imageReader.getSurface(), null, null
        );

        isStreaming = true;

        imageReader.setOnImageAvailableListener(reader -> {
            if (!isStreaming) return;

            Image image = null;
            Bitmap bitmap = null;
            try {
                image = reader.acquireLatestImage();
                if (image != null) {
                    int imgWidth = image.getWidth();
                    int imgHeight = image.getHeight();
                    final Image.Plane[] planes = image.getPlanes();
                    final ByteBuffer buffer = planes[0].getBuffer();
                    int pixelStride = planes[0].getPixelStride();
                    int rowStride = planes[0].getRowStride();
                    int rowPadding = rowStride - pixelStride * imgWidth;

                    Bitmap fullBitmap = Bitmap.createBitmap(
                            imgWidth + rowPadding / pixelStride,
                            imgHeight,
                            Bitmap.Config.ARGB_8888
                    );
                    fullBitmap.copyPixelsFromBuffer(buffer);

                    bitmap = Bitmap.createBitmap(fullBitmap, 0, 0, imgWidth, imgHeight);
                    fullBitmap.recycle();

                    // 发送 WebSocket
                    String base64Image = bitmapToBase64(bitmap);
                    JSONObject resp = new JSONObject();
                    resp.put("type", "screen");
                    resp.put("image", base64Image);
//                    webSocketServer.getInstance().sendToAll(resp.toString());
                    webSocketServer.getInstance().sendToActiveUser(resp.toString());

                    Log.d(TAG, "发送实时屏幕图像");
                }
            } catch (Exception e) {
                Log.e(TAG, "持续截图失败: " + e.getMessage());
            } finally {
                if (image != null) image.close();
                if (bitmap != null) bitmap.recycle();
            }
        }, new Handler(Looper.getMainLooper()));
    }

    public static void stopContinuousCapture() {
        if (!isStreaming) {
            return;
        }
        isStreaming = false;

        if (virtualDisplay != null) {
            virtualDisplay.release();
            virtualDisplay = null;
            Log.d(TAG, "VirtualDisplay 已释放");
        }

        if (imageReader != null) {
            imageReader.setOnImageAvailableListener(null, null);
            imageReader.close();
            imageReader = null;
            Log.d(TAG, "ImageReader 已释放");
        }
        Log.d(TAG, "停止持续截图");
        try{
            JSONObject res = new JSONObject();
            res.put("control", "close");
            webSocketServer.getInstance().sendToAll(res.toString());
        }catch (JSONException e) {
            Log.e(TAG, "构造关闭消息失败: " + e.getMessage());
        }
    }

    // ⬇️ 可选：base64 编码
    public static String bitmapToBase64(Bitmap bitmap) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, baos);
        byte[] bytes = baos.toByteArray();
        return Base64.encodeToString(bytes, Base64.NO_WRAP);
    }



    private static void stopProjection() {
        if (virtualDisplay != null) virtualDisplay.release();
//        if (mediaProjection != null) mediaProjection.stop();
//        mediaProjection = null;
//        virtualDisplay = null;
    }
}
