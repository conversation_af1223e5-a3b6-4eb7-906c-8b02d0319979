<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root_scrollview"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:fillViewport="true"
    android:orientation="vertical">
    <!-- 顶部LOGO -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:background="@color/blackRray"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/middleBackground"
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            android:src="@mipmap/ic_launcher_round" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:text="SPRTCODE"
            android:textColor="@color/white"
            android:textSize="20dp"
            android:textStyle="bold"></TextView>
    </LinearLayout>
    <!-- 表单项 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="2dp">
        <!-- 账号 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="4dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingRight="5sp"
                android:text="账号"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_marginBottom="4dp"/>

            <EditText
                android:id="@+id/login_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:inputType="text"
                android:textColor="@color/white"
                android:padding="8dp"
                android:textSize="16sp"
                android:background="@drawable/edittext_background"/>
        </LinearLayout>
        <!-- 密码 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="8dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="密码"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_marginBottom="4dp" />
<!--            android:inputType="textPassword"-->
            <EditText
                android:id="@+id/login_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:padding="8dp"
                android:textSize="16sp"
                android:textColor="@color/white"
                android:background="@drawable/edittext_background"/>
        </LinearLayout>
        <!-- IP -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingTop="8dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:paddingRight="5sp"
                android:text="IP"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_marginBottom="4dp"/>

            <EditText
                android:id="@+id/login_ip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="3"
                android:enabled="false"
                android:inputType="text"
                android:textColor="@color/blackGray"
                android:padding="8dp"
                android:text=""
                android:textSize="16sp"
                android:background="@drawable/edittext_background"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp">
        </LinearLayout>
        <!-- 底部按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:background="#3d3d3d"
            android:gravity="center_vertical"
            android:paddingLeft="10dp">
            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/login_register"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:background="@drawable/button_rounded_gray"
                android:paddingVertical="1dp"
                android:text="注册"
                android:textColor="@android:color/white"
                android:textSize="18sp"
                android:textStyle="bold"
                />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
