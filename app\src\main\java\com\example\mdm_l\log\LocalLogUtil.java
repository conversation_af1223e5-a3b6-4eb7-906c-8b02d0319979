package com.example.mdm_l.log;

import android.content.Context;
import android.util.Log;

import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class LocalLogUtil {
    private static final String TAG = "设备日志";
    private static final String ROOT_LOG_DIR = "com.example.mdm_l";

    public static void writeLog(Context context, String message, String type) {
        try {
            // 默认使用 default 类型
            if (type == null || type.trim().isEmpty()) {
                type = "default";
            }

            // 获取 App 私有目录 + 类型子目录
            File rootDir = new File(context.getExternalFilesDir(null), ROOT_LOG_DIR);
            File typeDir = new File(rootDir, type);  // com.example.mdm_l/heartbeat
            if (!typeDir.exists()) typeDir.mkdirs();

            // 日志文件名：log_2025-06-16.txt
            String fileName = "log_" + new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(new Date()) + ".txt";
            File logFile = new File(typeDir, fileName);

            // 写入日志
            FileWriter writer = new FileWriter(logFile, true);
            String timestamp = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date());
            writer.write(timestamp + " - " + message + "\n");
            writer.flush();
            writer.close();
        } catch (Exception e) {
            Log.e(TAG, "写入日志失败: " + e.getMessage());
        }
    }
}
