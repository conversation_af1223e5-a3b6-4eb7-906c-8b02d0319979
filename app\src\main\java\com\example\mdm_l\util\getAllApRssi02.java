package com.example.mdm_l.util;

import android.Manifest;
import android.content.Context;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class getAllApRssi02 {

    public static void scanWifi(Context context) {
        WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) {
            Log.e("WiFiScan", "WifiManager is null");
            return;
        }

        // 检查定位权限（必须）
        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e("WiFiScan", "缺少 ACCESS_FINE_LOCATION 权限");
            return;
        }

        // 检查 Wi-Fi 是否开启
        if (!wifiManager.isWifiEnabled()) {
            Log.w("WiFiScan", "WiFi 未开启，尝试开启");
            wifiManager.setWifiEnabled(true);
        }

        boolean success = wifiManager.startScan();
        if (!success) {
            Log.e("WiFiScan", "启动 WiFi 扫描失败");
            return;
        }

        // 获取最新的 Wi-Fi 扫描结果（注意：有可能是上次扫描结果）
        List<ScanResult> results = wifiManager.getScanResults();
        JSONArray wifiList = new JSONArray();
        Log.d("WiFiScan", "扫描到的 WiFi 数量：" + results.size());

        for (ScanResult result : results) {
            if (result.SSID != null && result.SSID.contains("sprt") && result.frequency > 4900) {
                try {
                    JSONObject wifiItem = new JSONObject();
                    wifiItem.put("ssid", result.SSID);
                    wifiItem.put("level", result.level);
                    wifiItem.put("bssid", result.BSSID);
                    wifiList.put(wifiItem);
                } catch (JSONException e) {
                    Log.e("WiFiScan", "构建 WiFi JSON 对象失败", e);
                }
            }
        }

        // 构建最终发送的 JSON 消息
        JSONObject resp = new JSONObject();
        try {
            resp.put("type", "wifiList");
            resp.put("wifi", wifiList);
        } catch (JSONException e) {
            Log.e("WiFiScan", "构建 JSON 失败", e);
        }

        // 通过 WebSocket 发送
        try {
            webSocketServer.getInstance().sendToAll(resp.toString());
            Log.i("WebSocket", "WiFi 列表发送成功: " + resp.toString());
        } catch (Exception e) {
            Log.e("WebSocket", "WiFi 列表发送失败", e);
        }
    }
}
