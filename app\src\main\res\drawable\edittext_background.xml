<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 聚焦时蓝色下划线 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <size android:height="1dp" />
            <corners android:radius="4dp" />
            <solid android:color="@android:color/transparent" />
            <stroke android:width="1dp" android:color="#2196F3" />
        </shape>
    </item>

    <!-- 默认白色下划线 -->
    <item>
        <shape android:shape="rectangle">
            <size android:height="1dp" />
            <corners android:radius="4dp" />
            <solid android:color="@android:color/transparent" />
            <stroke android:width="1dp" android:color="#252525" />
        </shape>
    </item>
</selector>
