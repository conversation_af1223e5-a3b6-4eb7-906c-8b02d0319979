package com.example.mdm_l;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.content.pm.ServiceInfo;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.example.mdm_l.util.WebSocketHandler;
import com.example.mdm_l.util.webSocketServer;

public class WebSocketService extends Service {
    private static final String CHANNEL_ID = "websocket_channel";
    private static final int NOTIFICATION_ID = 1001;

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 不支持绑定
    }

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("MDM")
                .setContentText("WebSocket后台运行中")
                .setSmallIcon(R.drawable.spcode2) // 使用你的图标
                .build();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(NOTIFICATION_ID, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC);
        } else {
            startForeground(NOTIFICATION_ID, notification);
        }

        initWebSocket();
    }
//    @Override
//    public int onStartCommand(Intent intent,int flags, int startId) {
//        return START_STICKY;
//    }
    private void initWebSocket() {
        webSocketServer server = webSocketServer.getInstance();
        ComponentName adminComponent = new ComponentName(this, MyDeviceAdminReceiver.class);
        WebSocketHandler.setup(null, this, server, adminComponent);
        server.startServer();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "WebSocket服务",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
}
