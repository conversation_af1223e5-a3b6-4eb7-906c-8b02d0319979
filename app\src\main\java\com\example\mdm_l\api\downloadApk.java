package com.example.mdm_l.api;

import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageInstaller;
import android.content.pm.PackageManager;
import android.os.Environment;
import android.util.Log;
import android.widget.Toast;

import com.example.mdm_l.domain.PostData;
import com.example.mdm_l.util.InstallResultReceiver;
import com.example.mdm_l.util.request;
import com.google.gson.Gson;

import org.json.JSONObject;
import org.json.JSONStringer;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class downloadApk {
    public static void handleDownloadApk(Context context, String apkName) {
        String apk_name = apkName.substring(apkName.lastIndexOf("/") + 1);
        File downloadFile = new File(context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS), apk_name);
        String base_url = request.getBase_url();
        String DOWNLOAD_URL = base_url + "push/apkDownload";
        String url = DOWNLOAD_URL;
        OkHttpClient okHttpClient = new OkHttpClient();

        PostData postData = new PostData();
        postData.setPath(apkName);

        Gson gson = new Gson();
        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json"),
                gson.toJson(postData)
        );
//        RequestBody requestBody = new FormBody.Builder()
//                .add("path", apkName)
//                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();

        okHttpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e("APK下载", "下载失败", e);
                new android.os.Handler(context.getMainLooper()).post(() ->
                        Toast.makeText(context, "下载失败", Toast.LENGTH_SHORT).show()
                );
            }

//            @Override
//            public void onResponse(Call call, Response response) throws IOException {
//                if (response.isSuccessful()) {
//                    // 获取文件名（从 Content-Disposition 里取）
//                    String disposition = response.header("Content-Disposition");
//                    String filename = apk_name;
//                    if (disposition != null && disposition.contains("filename*=")) {
//                        filename = apkName;
//                        filename = java.net.URLDecoder.decode(filename, "UTF-8");
//                    }
//                    // 保存文件
//                    InputStream is = response.body().byteStream();
//                    File downloadedFile = new File(context.getExternalFilesDir(null), filename);  // 或其他路径
//                    FileOutputStream fos = new FileOutputStream(downloadedFile);
//                    byte[] buffer = new byte[4096];
//                    int len;
//                    while ((len = is.read(buffer)) != -1) {
//                        fos.write(buffer, 0, len);
//                    }
//                    fos.flush();
//                    fos.close();
//                    is.close();
//
//                    Log.d("DOWNLOAD", "文件下载成功，路径：" + downloadedFile.getAbsolutePath());
//                } else {
//                    Log.e("DOWNLOAD", "下载失败：" + response.code());
//                }
//            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful()) {
                    Log.e("APK下载", "HTTP错误: " + response.code());
                    new android.os.Handler(context.getMainLooper()).post(() ->
                            Toast.makeText(context, "下载失败，错误码: " + response.code(), Toast.LENGTH_SHORT).show()
                    );
                    return;
                }
                try (InputStream inputStream = response.body().byteStream();
                     FileOutputStream fileOutputStream = new FileOutputStream(downloadFile)) {

                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        fileOutputStream.write(buffer, 0, bytesRead);
                    }

                    new android.os.Handler(context.getMainLooper()).post(() -> {
                        Toast.makeText(context, "下载成功", Toast.LENGTH_SHORT).show();
                        Log.i("APK下载", "文件路径: " + downloadFile.getAbsolutePath());
                        Log.i("APK下载", "文件大小: " + downloadFile.length() + " 字节");
                        // 可选：自动安装 APK
                        installApkSilently(context, downloadFile);
                    });

                } catch (IOException e) {
                    Log.e("APK下载", "写入失败", e);
                    new android.os.Handler(context.getMainLooper()).post(() ->
                            Toast.makeText(context, "写入失败", Toast.LENGTH_SHORT).show()
                    );
                }
            }
        });


    }

    public static void installApkSilently(Context context, File apkFile) {
        try {
            if (!apkFile.exists() || apkFile.length() == 0) {
                Log.e("静默安装", "APK 文件无效，路径: " + apkFile.getAbsolutePath());
                return;
            }

            // 可选：提前解析并确认 APK 有效
            PackageManager pm = context.getPackageManager();
            PackageInfo info = pm.getPackageArchiveInfo(apkFile.getAbsolutePath(), 0);
            if (info == null) {
                Log.e("静默安装", "APK 无法解析，可能不是有效的 APK 文件");
                return;
            }

            Log.i("静默安装", "准备安装: " + apkFile.getAbsolutePath());
            PackageInstaller packageInstaller = pm.getPackageInstaller();

            PackageInstaller.SessionParams params = new PackageInstaller.SessionParams(
                    PackageInstaller.SessionParams.MODE_FULL_INSTALL);

            int sessionId = packageInstaller.createSession(params);
            PackageInstaller.Session session = packageInstaller.openSession(sessionId);

            try (OutputStream out = session.openWrite("app_install", 0, -1);
                 InputStream in = new FileInputStream(apkFile)) {
                byte[] buffer = new byte[65536];
                int c;
                while ((c = in.read(buffer)) != -1) {
                    out.write(buffer, 0, c);
                }
                session.fsync(out);
            }

            Intent intent = new Intent(context, InstallResultReceiver.class);
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context, sessionId, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
            session.commit(pendingIntent.getIntentSender());

            Log.i("静默安装", "安装已提交");

        } catch (IOException e) {
            Log.e("静默安装", "安装失败", e);
        }
    }

    public static void unInstallPackage(Context context, String packageName) {
        try {
            PackageInstaller packageInstaller = context.getPackageManager().getPackageInstaller();

            // 创建一个广播 Intent 用于接收卸载结果
            Intent intent = new Intent(context, InstallResultReceiver.class);
            intent.putExtra("uninstall", true); // 标记这是卸载操作
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    context, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );

            // 提交卸载请求
            packageInstaller.uninstall(packageName, pendingIntent.getIntentSender());
            Log.i("静默卸载", "已提交卸载请求: " + packageName);

        } catch (Exception e) {
            Log.e("静默卸载", "卸载失败: " + packageName, e);
        }
    }

}

