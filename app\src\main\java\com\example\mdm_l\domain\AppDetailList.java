package com.example.mdm_l.domain;

public class AppDetailList {
    public String packageName;
    public String versionName;
    public String appName;
    public long installTime;

    public AppDetailList() {
    }

    public AppDetailList(String packageName, String versionName, String appName, long installTime) {
        this.packageName = packageName;
        this.versionName = versionName;
        this.appName = appName;
        this.installTime = installTime;
    }

    @Override
    public String toString() {
        return "AppDetailList{" +
                "packageName='" + packageName + '\'' +
                ", versionName='" + versionName + '\'' +
                ", appName='" + appName + '\'' +
                ", installTime=" + installTime +
                '}';
    }
}
