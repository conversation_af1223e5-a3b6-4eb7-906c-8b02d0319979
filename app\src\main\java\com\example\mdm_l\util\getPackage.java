package com.example.mdm_l.util;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.example.mdm_l.domain.AppDetailList;
import com.example.mdm_l.domain.AppInfo;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class getPackage {

    public static List<String> getInstalledAppList(Context context) {
        List<String> appList = new ArrayList<>();
        PackageManager pm = context.getPackageManager();
        List<PackageInfo> packages = pm.getInstalledPackages(0);

        for (PackageInfo packageInfo : packages) {
            // 可选：只列出非系统应用
            if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
//                String appName = pm.getApplicationLabel(packageInfo.applicationInfo).toString();
                String packageName = packageInfo.packageName;
                appList.add(packageName);
            }
        }
        return appList;
    }

    public static List<AppInfo> getInstallAppVersion(Context context) {
        List<AppInfo> appInfoList = new ArrayList<>();
        PackageManager pm = context.getPackageManager();
        List<PackageInfo> packages = pm.getInstalledPackages(0);

        for (PackageInfo packageInfo : packages) {
            // 只获取非系统应用
            if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) {
                String packageName = packageInfo.packageName;
                String versionName = packageInfo.versionName;
                appInfoList.add(new AppInfo(packageName, versionName));
            }
        }
        return appInfoList;
    }

    public static JSONArray getInstallAppDetailNoLocalList(Context context) { // 安装APP
        JSONArray appInfoList = new JSONArray();
        PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> packages = packageManager.getInstalledPackages(PackageManager.GET_META_DATA);
        for (PackageInfo packageInfo : packages) {
            if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) == 0) { // 除设备自带APP外
                try {
                    JSONObject appDetailItem = new JSONObject();
                    appDetailItem.put("packageName",packageInfo.packageName);
                    appDetailItem.put("versionName",packageInfo.versionName);
                    appDetailItem.put("appName",packageInfo.applicationInfo.loadLabel(packageManager).toString());
                    appDetailItem.put("firstInstallTime",packageInfo.firstInstallTime);
                    appInfoList.put(appDetailItem);
                }catch (Exception e) {
                    Log.e("获取设备APP列表详细信息", "构建 JSON 失败", e);
                }
            }
        }
        System.out.println(appInfoList);
        return appInfoList;
    }

    public static JSONArray getInstallAppDetailLocalList(Context context) { // 设备自带APP
        JSONArray appInfoList = new JSONArray();
        PackageManager packageManager = context.getPackageManager();
        List<PackageInfo> packages = packageManager.getInstalledPackages(PackageManager.GET_META_DATA);
        for (PackageInfo packageInfo : packages) {
            if ((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0) { // 除设备自带APP外
                try {
                    JSONObject appDetailItem = new JSONObject();
                    appDetailItem.put("packageName",packageInfo.packageName);
                    appDetailItem.put("versionName",packageInfo.versionName);
                    appDetailItem.put("appName",packageInfo.applicationInfo.loadLabel(packageManager).toString());
                    appDetailItem.put("firstInstallTime",packageInfo.firstInstallTime);
                    appInfoList.put(appDetailItem);
                }catch (Exception e) {
                    Log.e("获取设备APP列表详细信息", "构建 JSON 失败", e);
                }
            }
        }
        System.out.println(appInfoList);
        return appInfoList;
    }

}
