package com.example.mdm_l.util;
import android.content.Context;
import android.os.Build;
import java.net.InetAddress;
import java.net.UnknownHostException;
public class getModel {
    public static String getModelString(Context context) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) { // Android 8.0+
                return Build.MODEL; // 设备型号
            } else {
                return Build.SERIAL;
            }
        } catch (SecurityException e) {
            e.printStackTrace();
            return "无权限";
        } catch (Exception e) {
            e.printStackTrace();
            return "未知";
        }
    }
    public static String getDeviceNameString() {
        return Build.MANUFACTURER;
    }
}
