package com.example.mdm_l.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Criteria;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.util.Log;

import androidx.core.app.ActivityCompat;

public class LocationUtil {

    public static void getLocation(Context context) {
        LocationManager locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);

        if (ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED &&
                ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Log.e("NativeLocation", "没有定位权限");
            return;
        }

        Criteria criteria = new Criteria();
        criteria.setAccuracy(Criteria.ACCURACY_FINE);
        criteria.setPowerRequirement(Criteria.POWER_LOW);
        String provider = locationManager.getBestProvider(criteria, true);

        if (provider == null) {
            Log.e("NativeLocation", "没有可用的定位提供者");
            return;
        }

        // 获取最后一次定位
        Location lastKnown = locationManager.getLastKnownLocation(provider);
        if (lastKnown != null) {
            Log.i("NativeLocation", "【立即获取】经度: " + lastKnown.getLongitude() +
                    " 纬度: " + lastKnown.getLatitude() +
                    " 精度: " + lastKnown.getAccuracy());
        } else {
            Log.w("NativeLocation", "lastKnownLocation 为空");
        }

        // 注册全局监听器，避免被 GC 回收
        locationManager.requestLocationUpdates(provider, 5000, 1, locationListener);
    }
    private static final LocationListener locationListener = new LocationListener() {
        @Override
        public void onLocationChanged(Location location) {
            double lat = location.getLatitude();
            double lon = location.getLongitude();
            float accuracy = location.getAccuracy();

            Log.i("NativeLocation", "经度: " + lon + " 纬度: " + lat + " 精度: " + accuracy);
        }

        @Override public void onStatusChanged(String provider, int status, Bundle extras) {}
        @Override public void onProviderEnabled(String provider) {}
        @Override public void onProviderDisabled(String provider) {}
    };


}

