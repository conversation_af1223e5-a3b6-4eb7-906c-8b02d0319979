<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:padding="12dp"
    android:background="@android:color/white"
    android:elevation="10dp"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:backgroundTint="#F8F8F8"
    android:layout_marginLeft="12dp"
    android:layout_marginRight="12dp"
    >
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="管理员消息"
        android:textStyle="bold"
        android:textSize="20dp"
        android:textColor="#000"/>
    <TextView
        android:id="@+id/message_text"
        android:textSize="17sp"
        android:textColor="#222222"
        android:lineSpacingExtra="6dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="通知内容"
        android:layout_marginBottom="20dp" />

    <Button
        android:id="@+id/ok_button"
        android:layout_width="72dp"
        android:layout_height="40dp"
        android:layout_gravity="end"
        android:backgroundTint="#007AFF"
        android:text="我知道了"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        android:textSize="15sp" />
</LinearLayout>
