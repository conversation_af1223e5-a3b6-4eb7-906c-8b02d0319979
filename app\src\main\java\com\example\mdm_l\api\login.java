package com.example.mdm_l.api;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.util.Log;

import com.example.mdm_l.domain.LoginInfo;
import com.example.mdm_l.log.LocalLogUtil;
import com.example.mdm_l.util.request;
import com.example.mdm_l.util.setLocalInfo;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class login {
    public static final MediaType JSON = MediaType.parse("application/json; charset=utf-8");

    public static String addLogin(Context context, LoginInfo info) {
        String base_url = request.getBase_url();
        String LOGIN_URL  = base_url + "login";
        OkHttpClient client = new OkHttpClient();
        try {
            // 构造 JSON 数据
            JSONObject json = new JSONObject();
            json.put("username", info.username);
            json.put("password", info.password);
            json.put("assetCode", info.assetCode);
            json.put("mac", info.mac);
            json.put("ip", info.ip);
            json.put("sn", info.sn);
            json.put("model", info.model);
            json.put("platform", info.platform != null ? info.platform : "android");
            json.put("version", info.version);
            RequestBody requestBody = RequestBody.create(JSON, json.toString().getBytes(StandardCharsets.UTF_8));
            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(LOGIN_URL)
                    .post(requestBody)
                    .build();
            Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                String result = response.body().string();
                Log.d("LoginResult", "成功: " + result);
                setLocalInfo.setInfo(context, info);
                // 日志
                LocalLogUtil.writeLog(context, "登录日志"+ json.toString(),"login");
                return result;
            } else {
                Log.e("LoginResult", "失败: " + response.code());
                // 日志
                LocalLogUtil.writeLog(context, "登录日志"+ json.toString()+response.code(),"loginError");
                return "服务器错误：" + response.code();
            }
        } catch (Exception e) {
            // 日志
            LocalLogUtil.writeLog(context, "登录日志"+e.getMessage(),"loginError");
            e.printStackTrace();
            return "请求失败：" + e.getMessage();

        }
    }
}
