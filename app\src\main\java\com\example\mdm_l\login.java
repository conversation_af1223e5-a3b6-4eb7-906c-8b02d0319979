package com.example.mdm_l;

import android.app.Activity;
import android.app.ActivityManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.content.pm.SigningInfo;
import android.database.Cursor;
import android.graphics.Color;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.util.Xml;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;

import com.example.mdm_l.domain.LoginInfo;
import com.example.mdm_l.log.LocalLogUtil;
import com.example.mdm_l.util.DeviceNameUtil;
import com.example.mdm_l.util.ScreenshotUtil;
import com.example.mdm_l.util.ToastUtil;
import com.example.mdm_l.util.WebSocketHandler;
import com.example.mdm_l.util.getIp;
import com.example.mdm_l.util.getMAC;
import com.example.mdm_l.util.getModel;
import com.example.mdm_l.util.getSN;
import com.example.mdm_l.util.getVersion;
import com.example.mdm_l.util.heartbest;
import com.example.mdm_l.util.setLocalInfo;
import com.example.mdm_l.util.setSound;
import com.example.mdm_l.util.webSocketServer;

import static com.example.mdm_l.api.login.addLogin;
import com.symbol.emdk.EMDKManager;
import com.symbol.emdk.EMDKResults;
import com.symbol.emdk.ProfileManager;
import com.symbol.emdk.EMDKManager.EMDKListener;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.StringReader;
import java.util.Base64;


public class login extends AppCompatActivity implements EMDKListener {
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1001;
    EditText login_ip; // IP控件
    EditText login_username; // 组名
    EditText login_password; // 密码
    Button login_register; // 注册按钮
    String model; // 设备型号
    String ip; // ip
    String mac; // mac
    String sn; // sn
    String platform = "Android"; // 设备类型
    String version; // 设备版本号 10
    String username; // 账号
    String password; // 密码
    String assetCode; // 资产编码
    heartbest heartbeat;
    // 授权SN-MAC
    private EMDKManager emdkManager = null;
    private String parmName = "";
    private String errorDescription = "";
    private String errorString = "";
    private ProfileManager profileManager = null;
    private String errorType = "";



    @Override
    protected void onCreate(Bundle savedInstanceState){
        super.onCreate(savedInstanceState);
        setContentView(R.layout.login);

        EMDKResults results = EMDKManager.getEMDKManager(getApplicationContext(), this);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            Window window = getWindow();
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
            window.setStatusBarColor(Color.parseColor("#000000")); // 黑色
        }
        // 日志
        LocalLogUtil.writeLog(this, "打开软件", "openApp");
        login_ip = findViewById(R.id.login_ip); // 设备ip
        login_username = findViewById(R.id.login_username); // 组名
        login_password = findViewById(R.id.login_password); // 密码
        login_register = findViewById(R.id.login_register); // 注册按钮
        LoginInfo firstInfo = setLocalInfo.getInfo(this); // 获取本地存储数据
        if (firstInfo.password != null && firstInfo.username != null) {
            login_username.setText(firstInfo.username); // 输入框设置保存的账号
            login_password.setText(firstInfo.password); // 输入框设置保存的账号
            login_username.setEnabled(false); // 将账号 回显到页面上 设置输入框禁用
            login_username.setTextColor(Color.parseColor("#4d4d4d"));
            login_password.setEnabled(false); // 将密码 回显到页面上 设置输入框禁用
            login_password.setTextColor(Color.parseColor("#4d4d4d"));
            login_register.setEnabled(false); // 将登录  设置按钮框禁用
            login_register.setTextColor(Color.parseColor("#4d4d4d"));
            Toast.makeText(this, "该设备已注册", Toast.LENGTH_SHORT).show();
        }
        // 登录点击事件
        login_register.setOnClickListener(v -> {
            if ((username == null || username.trim().isEmpty()) || (password == null || password.trim().isEmpty())) {
                ToastUtil.showWarning(this, "账号密码不能为空");
                grantPermissionTo("com.example.mdm_l");
                String URI_SN = "content://oem_info/oem.zebra.secure/build_serial";
                String URI_WIFI_MAC = "content://oem_info/oem.zebra.secure/wifi_mac";
                String sn_ = RetrieveOEMInfo(Uri.parse(URI_SN), false);
                String mac_ = RetrieveOEMInfo(Uri.parse(URI_WIFI_MAC), false);
                sn = sn_;
                mac = mac_;
                System.out.println(sn_);
                System.out.println(mac_);
                LoginInfo info = new LoginInfo();
                info.sn = sn_;
                info.mac = mac_;
                setLocalInfo.setInfo(this, info);
            } else {
                // 调用接口 成功注册 将账号密码 SN MAC存储本地
                grantPermissionTo("com.example.mdm_l");
                String URI_SN = "content://oem_info/oem.zebra.secure/build_serial";
                String URI_WIFI_MAC = "content://oem_info/oem.zebra.secure/wifi_mac";
                String sn_ = RetrieveOEMInfo(Uri.parse(URI_SN), false);
                String mac_ = RetrieveOEMInfo(Uri.parse(URI_WIFI_MAC), false);
                sn = sn_;
                mac = mac_;
                LoginInfo info = new LoginInfo();
                info.username = username;
                info.password = password;
                info.ip = ip;
                info.model = model;
                info.assetCode = assetCode;
                info.platform = platform;
                info.version = version;
                info.mac = mac_;
                info.sn = sn_;
                setLocalInfo.setInfo(this, info);
                new Thread(() -> {
                    String result = addLogin(this, info);
                    runOnUiThread(() -> {
                        Toast.makeText(this, result, Toast.LENGTH_SHORT).show();
                    });
                }).start();
            }
        });
        // 启动 HeartbeestService（心跳服务）
        Intent hbIntent = new Intent(this, HeartbeatService.class);
        startForegroundService(hbIntent);
//        WifiMacHelper.forceUseDeviceMac(this);
        // 处理数据
        handleLocal(this);
        // 启动webSocket服务 // webSocket 代码开始
        webSocketServer server = webSocketServer.getInstance();
        ComponentName adminComponent = new ComponentName(this, MyDeviceAdminReceiver.class);
        WebSocketHandler.setup(this, this, server, adminComponent);
        if (!server.isStarted()) {
            try {
                server.start();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        // webSocket 代码到这里结束
        // 授权  远程
        ScreenshotUtil.requestScreenshotPermission(this);
        // 授权  悬浮窗
        if (!Settings.canDrawOverlays(this)) {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                    Uri.parse("package:" + this.getPackageName()));
            this.startActivity(intent);
        }
    }

//    public static String getAppSignatureBase64(Context context) {
//        try {
//            PackageInfo packageInfo = context.getPackageManager().getPackageInfo(
//                    context.getPackageName(),
//                    PackageManager.GET_SIGNING_CERTIFICATES
//            );
//            Signature[] signatures = packageInfo.signingInfo.getApkContentsSigners();
//            byte[] cert = signatures[0].toByteArray();
//            return Base64.getEncoder().encodeToString(cert);
//        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
//        }
//    }


    // ScreenshotUtil.java 中的回调处理
    @RequiresApi(api = Build.VERSION_CODES.O)
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == 1001 && resultCode == Activity.RESULT_OK) {
            Intent serviceIntent = new Intent(this, ScreenshotService.class);
            serviceIntent.putExtra("resultCode", resultCode);
            serviceIntent.putExtra("data", data); // 必须是 Parcelable
            startForegroundService(serviceIntent);
        }
    }


    @Override
    protected void onStart() {
        super.onStart();
        // 页面上 设置ip
        login_ip.setText(ip);
        // 页面上 设置mac
    }

    // 处理数据
    public void handleLocal(Context context) {
        model = getModel.getModelString(context); // 设备型号
        ip = getIp.getIpString(context); // ip地址
//        mac = getMAC.getMACString(context); // mac地址 网络MAC地址
//        sn = getSN.getSerialNumber(context); // sn设备序列号
        version = getVersion.getSDKString(); // 设备安卓版本号 10
        login_username.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                username = s.toString();
            }
        });
        login_password.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                password = s.toString();
            }
        });
    }

    public static boolean isServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        setSound.stop(); // 声音
        // 日志
        LocalLogUtil.writeLog(this, "关闭软件后台", "openApp");
//        webSocketServer.getInstance().stopServer(); // webSocket
//        heartbeat.stopHeartbeat(); // 心跳
    }

    private void grantPermissionTo(String targetPackageName) {
        String targetSignature = getAppSigByPackageName(targetPackageName);
        if (profileManager == null) {
            Log.e("EMDK", "profileManager 尚未初始化完成，不能执行授权");
            return;
        }
        if (TextUtils.isEmpty(targetSignature)) {
            return;
        }

        String[] modifyData = new String[1];
        modifyData[0] =
                "<?xml version=\"1.0\" encoding=\"utf-8\"?>" +
                        "<characteristic type=\"Profile\">" +
                        "<parm name=\"ProfileName\" value=\"OEM-Service\"/>" +
                        "<characteristic version=\"11.9\" type=\"AccessMgr\">" +
                        "    <parm name=\"ServiceAccessAction\" value=\"4\" />" +
                        "    <parm name=\"ServiceIdentifier\" value=\"content://oem_info/oem.zebra.secure/build_serial\" />" +
                        "    <parm name=\"CallerPackageName\" value=\"" + targetPackageName + "\" />" +
                        "    <parm name=\"CallerSignature\" value=\"" + targetSignature + "\" />" +
                        "</characteristic>" +
                        "<characteristic version=\"11.9\" type=\"AccessMgr\">" +
                        "    <parm name=\"ServiceAccessAction\" value=\"4\" />" +
                        "    <parm name=\"ServiceIdentifier\" value=\"content://oem_info/oem.zebra.secure/wifi_mac\" />" +
                        "    <parm name=\"CallerPackageName\" value=\"" + targetPackageName + "\" />" +
                        "    <parm name=\"CallerSignature\" value=\"" + targetSignature + "\" />" +
                        "</characteristic>" +
                        "</characteristic>";

        new AsyncTask<String, Void, EMDKResults>() {
            @Override
            protected EMDKResults doInBackground(String... strings) {
                return profileManager.processProfile("OEM-Service", ProfileManager.PROFILE_FLAG.SET, strings);
            }

            @Override
            protected void onPostExecute(EMDKResults results) {
                String resultString = "";
                if (results.statusCode == EMDKResults.STATUS_CODE.CHECK_XML) {
                    try {
                        XmlPullParser parser = Xml.newPullParser();
                        parser.setInput(new StringReader(results.getStatusString()));
                        parseXML(parser);
                        if (TextUtils.isEmpty(parmName) && TextUtils.isEmpty(errorType) && TextUtils.isEmpty(errorDescription)) {
                            resultString = "授权成功：已授权 " + targetPackageName;
                        } else {
                            resultString = "授权失败：" + errorString;
                        }
                    } catch (XmlPullParserException e) {
                        resultString = "解析授权结果异常：" + e.getMessage();
                    }
                } else {
                    resultString = "授权失败：EMDK返回错误代码：" + results.statusCode;
                }
            }
        }.execute(modifyData[0]);
    }
    public void parseXML(XmlPullParser myParser) {
        int event;
        try {
            // Retrieve error details if parm-error/characteristic-error in the response XML
            event = myParser.getEventType();
            while (event != XmlPullParser.END_DOCUMENT) {
                String name = myParser.getName();
                switch (event) {
                    case XmlPullParser.START_TAG:

                        if (name.equals("parm-error")) {
                            parmName = myParser.getAttributeValue(null, "name");
                            errorDescription = myParser.getAttributeValue(null, "desc");
                            errorString = " (Name: " + parmName + ", Error Description: " + errorDescription + ")";
                            return;
                        }

                        if (name.equals("characteristic-error")) {
                            errorType = myParser.getAttributeValue(null, "type");
                            errorDescription = myParser.getAttributeValue(null, "desc");
                            errorString = " (Type: " + errorType + ", Error Description: " + errorDescription + ")";
                            return;
                        }

                        break;
                    case XmlPullParser.END_TAG:

                        break;
                }
                event = myParser.next();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    private String getAppSigByPackageName(String pkgName){
        String Base64format = "";
        try {
            SigningInfo signingInfo = getPackageManager().getPackageInfo(pkgName, PackageManager.GET_SIGNING_CERTIFICATES).signingInfo;
            Signature[] sigs = signingInfo.getApkContentsSigners();
            for (Signature sig : sigs) {
                Base64format = Base64.getEncoder().encodeToString(sig.toByteArray());
                Log.d("Target App Signature", Base64format);
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e("getAppSigByPackageName", "App not found: " + e.getMessage());
        }
        return Base64format;
    }
    private String RetrieveOEMInfo(Uri uri, boolean isMAC) {
        //  For clarity, this code calls ContentResolver.query() on the UI thread but production code should perform queries asynchronously.
        //  See https://developer.android.com/guide/topics/providers/content-provider-basics.html for more information
        Cursor cursor = getContentResolver().query(uri, null, null, null, null);
        String status = "";
        if (cursor == null || cursor.getCount() < 1) {
            String errorMsg = "Error: This app does not have access to call OEM service. " +
                    "Please assign access to " + uri + " through MX.  See ReadMe for more information";
            Log.d("获取SN", errorMsg);
            status = errorMsg;
            return status;
        }
        while (cursor.moveToNext()) {
            if (cursor.getColumnCount() == 0) {
                //  No data in the cursor.  I have seen this happen on non-WAN devices
                String errorMsg = "Error: " + uri + " does not exist on this device";
                Log.d("获取SN", errorMsg);
                if (isMAC)
                    errorMsg = "Error: Could not find WiFi mac.  Is device WLAN capable?";
                status = errorMsg;
            } else {
                Log.v("获取SN", "column count is " + cursor.getColumnCount());
                for (int i = 0; i < cursor.getColumnCount(); i++) {
                    Log.v("获取SN", "column " + i + "=" + cursor.getColumnName(i));
                    try {
                        String data = cursor.getString(cursor.getColumnIndex(cursor.getColumnName(i)));
                        Log.i("获取SN", "Column Data " + i + "=" + data);
                        status = data;
                    } catch (Exception e) {
                        Log.i("获取SN", "Exception reading data for column " + cursor.getColumnName(i));
                    }
                }
            }
        }
        cursor.close();
        return status;
    }
    @Override
    public void onOpened(EMDKManager emdkManager) {
        this.emdkManager = emdkManager;
        this.profileManager = (ProfileManager) emdkManager.getInstance(EMDKManager.FEATURE_TYPE.PROFILE);
        Log.d("EMDK", "EMDK 成功初始化 profileManager");
    }

    @Override
    public void onClosed() {

    }
}