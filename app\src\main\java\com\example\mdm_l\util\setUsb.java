package com.example.mdm_l.util;

import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.UserManager;
import android.util.Log;

import com.example.mdm_l.MyDeviceAdminReceiver;

public class setUsb {
    public static final String TAG = "设置USB";

    /**
     * 禁用 USB 文件传输，仅保留充电功能
     */
    public static void noUsb(Context context) {
        try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
            ComponentName adminComponent = new ComponentName(context, MyDeviceAdminReceiver.class);

            if (dpm != null && dpm.isDeviceOwnerApp(context.getPackageName())) {
                dpm.addUserRestriction(adminComponent, UserManager.DISALLOW_USB_FILE_TRANSFER);
                Log.d(TAG, "USB 文件传输已禁用，仅允许充电");
            } else {
                Log.e(TAG, "应用不是设备所有者，无法禁用 USB");
            }
        } catch (Exception e) {
            Log.e(TAG, "禁用 USB 失败：" + e.getMessage());
        }
    }

    /**
     * 启用 USB 文件传输，恢复正常 USB 功能
     */
    public static void yesUsb(Context context) {
        try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
            ComponentName adminComponent = new ComponentName(context, MyDeviceAdminReceiver.class);

            if (dpm != null && dpm.isDeviceOwnerApp(context.getPackageName())) {
                dpm.clearUserRestriction(adminComponent, UserManager.DISALLOW_USB_FILE_TRANSFER);
                Log.d(TAG, "USB 文件传输已恢复，USB 功能正常");
            } else {
                Log.e(TAG, "应用不是设备所有者，无法启用 USB");
            }
        } catch (Exception e) {
            Log.e(TAG, "启用 USB 失败：" + e.getMessage());
        }
    }
}
