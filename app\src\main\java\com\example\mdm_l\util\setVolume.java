package com.example.mdm_l.util;

import android.content.Context;
import android.media.AudioManager;
import android.util.Log;

public class setVolume {
    public static final String TAG = "设置音量";
    // 增加音量
    public static void setAddVolume(Context context) {
        try {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager != null) {
                audioManager.adjustStreamVolume(
                        AudioManager.STREAM_MUSIC,
                        AudioManager.ADJUST_RAISE,
                        AudioManager.FLAG_SHOW_UI
                );
                Log.d(TAG, "音量减少");
            }
        } catch (Exception e) {
            Log.e(TAG, "增加音量失败" + e.getMessage());
        }
    }
    // 降低音量
    public static void setReduceVolume(Context context) {
        try {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if(audioManager != null) {
                audioManager.adjustStreamVolume(
                        AudioManager.STREAM_MUSIC,
                        AudioManager.ADJUST_LOWER,
                        AudioManager.FLAG_SHOW_UI
                );
            }
        } catch (Exception e) {
            Log.e(TAG, "降低音量失败" + e.getMessage());
        }
    }
    // 静音
    public static void setMuteVolume(Context context) {
        try {
            AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
            if (audioManager != null) {
                // 判断 Android 版本是否支持 ADJUST_MUTE
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    audioManager.adjustStreamVolume(
                            AudioManager.STREAM_MUSIC,
                            AudioManager.ADJUST_MUTE,
                            0
                    );
                } else {
                    // 旧版 Android，直接设置音量为 0
                    audioManager.setStreamVolume(AudioManager.STREAM_MUSIC, 0, 0);
                }
                Log.d(TAG, "已静音");
            }
        } catch (Exception e) {
            Log.e(TAG, "静音失败：" + e.getMessage());
        }
    }
}
