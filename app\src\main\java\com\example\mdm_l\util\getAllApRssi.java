package com.example.mdm_l.util;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.net.wifi.ScanResult;
import android.net.wifi.WifiManager;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class getAllApRssi {
    private static final String TAG = "WifiScanner";
    private static WifiManager wifiManager;
    private static Context appContext;
    private static long lastScanTime = 0;
    private static final long MIN_SCAN_INTERVAL_MS = 30 * 1000; // 30 秒限制

    private static final BroadcastReceiver wifiScanReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            boolean success = intent.getBooleanExtra(WifiManager.EXTRA_RESULTS_UPDATED, false);
            if (success) {
                handleScanResults();
            } else {
                Log.e(TAG, "WiFi 扫描失败或未更新");
            }
        }
    };

    public static void init(Context context) {
        appContext = context.getApplicationContext();
        wifiManager = (WifiManager) appContext.getSystemService(Context.WIFI_SERVICE);
        if (wifiManager == null) {
            Log.e(TAG, "无法获取 WifiManager");
            return;
        }

        // 注册广播监听 Wi-Fi 扫描完成事件
        IntentFilter filter = new IntentFilter(WifiManager.SCAN_RESULTS_AVAILABLE_ACTION);
        try {
//            appContext.registerReceiver(wifiScanReceiver, filter);
        } catch (Exception e) {
            Log.e(TAG, "广播注册失败", e);
        }
    }

    public static void scanWifi() {
        if (wifiManager == null) {
            Log.e(TAG, "WifiManager 未初始化");
            return;
        }

        // 权限检查
        if (ActivityCompat.checkSelfPermission(appContext, Manifest.permission.ACCESS_FINE_LOCATION)
                != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "缺少 ACCESS_FINE_LOCATION 权限");
            return;
        }

        long currentTime = System.currentTimeMillis();
        if (currentTime - lastScanTime < MIN_SCAN_INTERVAL_MS) {
            Log.w(TAG, "扫描过于频繁，已忽略本次请求");
            return;
        }

        lastScanTime = currentTime;

        boolean success = wifiManager.startScan();
        if (!success) {
            Log.e(TAG, "启动 WiFi 扫描失败");
        } else {
            Log.i(TAG, "WiFi 扫描请求已发起");
        }
    }

    private static void handleScanResults() {
        List<ScanResult> results = wifiManager.getScanResults();
        JSONArray wifiList = new JSONArray();

        for (ScanResult result : results) {
            if (result.SSID != null && result.SSID.contains("sprt") && result.frequency > 4900) {
                try {
                    JSONObject wifiItem = new JSONObject();
                    wifiItem.put("ssid", result.SSID);
                    wifiItem.put("level", result.level);
                    wifiItem.put("bssid", result.BSSID);
                    wifiList.put(wifiItem);
                } catch (JSONException e) {
                    Log.e(TAG, "构建 JSON 失败", e);
                }
            }
        }

        JSONObject resp = new JSONObject();
        try {
            resp.put("type", "wifiList");
            resp.put("wifi", wifiList);
        } catch (JSONException e) {
            Log.e(TAG, "构建最终 JSON 失败", e);
        }

        // 通过 WebSocket 发送
        try {
            webSocketServer.getInstance().sendToAll(resp.toString());
            Log.i(TAG, "WiFi 列表发送成功: " + resp.toString());
        } catch (Exception e) {
            Log.e(TAG, "WiFi 列表发送失败", e);
        }
    }

    public static void destroy() {
        try {
            appContext.unregisterReceiver(wifiScanReceiver);
            Log.i(TAG, "WiFi 扫描广播已注销");
        } catch (Exception e) {
            Log.e(TAG, "注销广播失败", e);
        }
    }
}
