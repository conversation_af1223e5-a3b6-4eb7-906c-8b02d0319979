// 处理webSocket 消息
package com.example.mdm_l.util;

import android.app.Activity;
import android.app.NotificationManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.annotation.Nullable;

import com.example.mdm_l.ClickAccessibilityService;
import com.example.mdm_l.api.downloadApk;
import com.example.mdm_l.domain.AppInfo;
import com.example.mdm_l.domain.FileInfo;
import com.example.mdm_l.domain.LoginInfo;
import com.example.mdm_l.domain.WebSocketMessage;
import com.example.mdm_l.log.LocalLogUtil;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class WebSocketHandler {
    private static final String TAG = "WebSocketHandler";

    public static void setup(@Nullable Activity activity, Context context, webSocketServer server, ComponentName adminConponent) {
        Log.d("初始化成功", "成功");
        server.setListener(new webSocketServer.ServerListener() {
            @Override
            public void onClientMessage(String message) {
                Log.d(TAG, "收到消息: " + message);
                try {
                    // 解析 JSON 数组
                    List<WebSocketMessage> list = new Gson().fromJson(message, new TypeToken<List<WebSocketMessage>>() {}.getType());
                    for (WebSocketMessage msg : list) {
                        handleMessage(activity, context, msg, adminConponent);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "JSON 解析失败: " + e.getMessage());
                }
            }

            @Override
            public void onClientConnected(String ip) {
                Log.d(TAG, "客户端连接: " + ip);
            }

            @Override
            public void onClientDisconnected(String ip) {
                Log.d(TAG, "客户端断开: " + ip);
            }
        });
    }

    // 可以在这里扩展处理不同 type 的逻辑
    private static void handleMessage(@Nullable Activity activity, Context context, WebSocketMessage msg, ComponentName adminComponent) {
        switch (msg.type) {
            case "get":
                if ("apkList".equals(msg.value)) {// 获取apk列表
                    // 日志
                    LocalLogUtil.writeLog(context, "请求设备安装APK列表", "control");
                    try {
                        List<String> apps = getPackage.getInstalledAppList(context);
                        JSONArray appArray = new JSONArray(apps); // 转为 JSON 数组
                        JSONObject resp = new JSONObject();
                        resp.put("type", "apkList");
                        resp.put("apps", appArray);
//                        webSocketServer.getInstance().sendToAll(resp.toString());
                        webSocketServer.getInstance().sendToActiveUser(resp.toString());
                        Log.d("WS-Server", "发送应用列表成功");
                        // 日志
                        LocalLogUtil.writeLog(context, "发送设备安装APK列表" + resp.toString(), "control");
                    } catch (Exception e) {
                        // 日志
                        LocalLogUtil.writeLog(context, "获取设备安装APK列表失败", "error");
                        Log.e(TAG, "生成或发送应用列表时出错: " + e.getMessage());
                    }
                } else if ("apkListDetailNoLocal".equals(msg.value)) { // 获取apk详细列表 用户安装
                    // 日志
                    LocalLogUtil.writeLog(context, "请求设备安装APK列表详细（用户安装）", "control");
                    try {
                        JSONArray appArray = getPackage.getInstallAppDetailNoLocalList(context); // 转为 JSON 数组
                        JSONObject resp = new JSONObject();
                        resp.put("type", "apkListDetailNoLocal");
                        resp.put("appList", appArray);
                        webSocketServer.getInstance().sendToActiveUser(resp.toString());
                    }catch (Exception e) {
                        // 日志
                        LocalLogUtil.writeLog(context, "获取设备安装APK列表详细（用户安装）失败", "error");
                    }
                } else if ("apkListDetailLocal".equals(msg.value)) { // 获取apk详细列表  设备自带
                    // 日志
                    LocalLogUtil.writeLog(context, "请求设备安装APK列表详细（本机自带）", "control");
                    try {
                        JSONArray appArray = getPackage.getInstallAppDetailLocalList(context); // 转为 JSON 数组
                        JSONObject resp = new JSONObject();
                        resp.put("type", "apkListDetailLocal");
                        resp.put("appList", appArray);
                        webSocketServer.getInstance().sendToActiveUser(resp.toString());
                    }catch (Exception e) {
                        // 日志
                        LocalLogUtil.writeLog(context, "获取设备安装APK列表详细（本机自带）失败", "error");
                    }
                } else if ("screenshot".equals(msg.value)) {
                    // 日志
                    LocalLogUtil.writeLog(context, "请求设备截图", "control");
//                    Log.d(TAG, "截图成功");
                    if (ScreenshotUtil.isReady()) {
                        ScreenshotUtil.captureScreen(context);
                        // 日志
                        LocalLogUtil.writeLog(context, "发送设备截图", "control");
                    } else {
                        // 日志
                        LocalLogUtil.writeLog(context, "设备未授权，请先授权截屏权限", "error");
                    }
                } else if ("location".equals(msg.value)) {
//                    Log.d(TAG, "获取位置信息");
                    // 日志
                    LocalLogUtil.writeLog(context, "请求位置信息", "control");
                    // getAllApRssi02.scanWifi(context);
                    getAllApRssi.scanWifi();
                }
                break;
            case "download": // 下载
//                Log.d(TAG, msg.valueList.toString());
                // 日志
                LocalLogUtil.writeLog(context, "请求下载指令", "control");
                System.out.println(msg.checkedPackage);
                if(msg.checkedPackage) { // 替换设备上已存在的APP
                    for (FileInfo fileInfo : msg.fileInfoList) {
                        downloadApk.handleDownloadApk(context, fileInfo.filePath);
                        // 日志
                        LocalLogUtil.writeLog(context, "下载APK" + fileInfo.filePath, "control");
                    }
                }else { // 保留设备上的APP
                    List<AppInfo> StringList = getPackage.getInstallAppVersion(context);
                    List<String> result = new ArrayList();
                    for (FileInfo fileInfo : msg.fileInfoList) {
                        Boolean flag = true;
                        for (AppInfo appInfo : StringList) {
                            if(appInfo.packageName.equals(fileInfo.packageName) && appInfo.versionName.equals(fileInfo.version)) {
                               flag = false;
                               break;
                            }
                        }
                        if (flag) result.add(fileInfo.filePath);
                    }
                    for (String r : result) {
                        System.out.println(r);
                        downloadApk.handleDownloadApk(context, r);
                    }
//                    for (FileInfo fileInfo : msg.fileInfoList) { // 遍历拿到的数据（该组的所有APP）
//                        for (Object stringList : StringList) {
//                            if(stringList.equals(fileInfo.packageName)) {
//                                Log.d("正要下载", fileInfo.filePath);
//                                downloadApk.handleDownloadApk(context, fileInfo.filePath);
//                                LocalLogUtil.writeLog(context, "下载APK" + fileInfo.filePath, "control");
//                            }
//                            break;
//                        }
//                    }
                }
                break;
            case "unDownload": // 卸载
//                Log.d(TAG, msg.valueList.toString());
                // 日志
                LocalLogUtil.writeLog(context, "请求卸载指令", "control");
                for (String item : msg.valueList) {
                    downloadApk.unInstallPackage(context, item);
                    // 日志
                    LocalLogUtil.writeLog(context, "卸载APK" + item, "control");
                }
                break;
            case "set":
                Log.d(TAG, msg.value);
                // 日志
                LocalLogUtil.writeLog(context, "请求控制指令" + msg.value, "control");
                if ("openSound".equals(msg.value)) { // 报警开
//                    Log.d(TAG, "发出声音");
                    setSound.play(context);
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                } else if ("closeSound".equals(msg.value)) { // 报警关
//                    Log.d(TAG, "关闭声音");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setSound.stop();
                    NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                    if (manager != null) {
                        manager.cancel(2001); // 2001 是通知栏通知的ID
                    }
                } else if ("openScreen".equals(msg.value)) { // 点亮屏幕
//                    Log.d(TAG, "开启屏");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    new setScreen().turnOnScreen(activity);
                } else if ("closeScreen".equals(msg.value)) { // 锁屏
//                    Log.d(TAG, "关闭屏");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    new setScreen().turnOffScreen(context, adminComponent);
                } else if ("shutdown".equals(msg.value)) { // 关机
//                    Log.d(TAG,"设备关机");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setShutdown.shutdown(context, adminComponent);
                } else if ("addVolume".equals(msg.value)) { // 提高音量
//                    Log.d(TAG, "提高音量指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setVolume.setAddVolume(context);
                } else if ("reduceVolume".equals(msg.value)) { // 降低音量
//                    Log.d(TAG, "降低音量指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setVolume.setReduceVolume(context);
                } else if ("muteVolume".equals(msg.value)) { // 静音
//                    Log.d(TAG, "静音指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setVolume.setMuteVolume(context);
                } else if ("noUsb".equals(msg.value)) { // 禁用usb
//                    Log.d(TAG, "禁用usb指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setUsb.noUsb(context);
                } else if ("yesUsb".equals(msg.value)) { // 启用usb
//                    Log.d(TAG, "启用usb指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setUsb.yesUsb(context);
                } else if ("noBluetooch".equals(msg.value)) { // 禁用蓝牙
//                    Log.d(TAG, "启用蓝牙指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setBluetooch.disableBluetooth(context);
                } else if ("yesBluetooch".equals(msg.value)) { // 启用蓝牙
//                    Log.d(TAG, "启用蓝牙指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setBluetooch.enableBluetooth(context);
                } else if ("noCamera".equals(msg.value)) { // 禁用摄像头
//                    Log.d(TAG, "启用摄像头指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setCamera.noCamera(context);
                } else if ("yesCamera".equals(msg.value)) { // 启用摄像头
//                    Log.d(TAG, "启用摄像头指令已接收");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + msg.value + "成功", "controlSuccess");
                    setCamera.yesCamera(context);
                }
                break;
            case "control": // 开始远程控制
                if (msg.value == null || msg.value.trim().isEmpty()) {
//                    Log.d(TAG, "初次连接");
                    // 日志
                    LocalLogUtil.writeLog(context, "控制指令" + "设备远程", "controlSuccess");
                    if (ScreenshotUtil.isReady()) {
                        ScreenshotUtil.startContinuousCaptureAndSend(context);
                    } else {
                        Log.e(TAG, "请先授权截取屏幕");
                    }
                } else {
                    String[] parts = msg.value.split(",");
                    if (parts.length == 2) {
                        try {
                            int x = Integer.parseInt(parts[0]);
                            int y = Integer.parseInt(parts[1]);
//                            ClickAccessibilityService.instance.clickPercent(x, y);
                        } catch (NumberFormatException e) {
                            Log.e(TAG, "坐标解析失败" + e.getMessage());
                            // 日志
                            LocalLogUtil.writeLog(context, "控制指令" + "坐标解析失败", "error");
                        }
                    } else if (parts.length == 4) {
                        Log.d(TAG, "滑动");
                        int x = Integer.parseInt(parts[0]);
                        int y = Integer.parseInt(parts[1]);
                        int endX = Integer.parseInt(parts[2]);
                        int endY = Integer.parseInt(parts[3]);
                        System.out.println(x + "-" + y + "-" + endX + "-" + endY);
                        ClickAccessibilityService.instance.swipePercent(x, y, endX, endY, 100);

                    } else {
                        Log.e(TAG, "无效坐标" + msg.value);
                        // 日志
                        LocalLogUtil.writeLog(context, "控制指令" + "无效坐标", "error");
                    }
                }
                break;
            case "cellPhone": // 接收pc客户端发送的消息
                new Handler(Looper.getMainLooper()).post(() -> {
                    showMessageWindow.showFloatingWindow(context.getApplicationContext(), msg.value);
                    webSocketServer.getInstance().sendToActiveUser("显示成功");
                });
                // 日志
                LocalLogUtil.writeLog(context, "控制指令" + msg.value, "controlMessage");
                break;
            case "server": // 获取服务器信息  用于修改组 账号密码
                if ("setUsername".equals(msg.value)) {
                    if (!msg.valueList.isEmpty() && 2 == msg.valueList.size()) {
                        String username = msg.valueList.get(0);
                        String password = msg.valueList.get(1);
                        LoginInfo loginInfo = new LoginInfo();
                        loginInfo.username = username;
                        loginInfo.password = password;
                        setLocalInfo.setInfo(context, loginInfo); // 保存账号密码
                        // 日志
                        LocalLogUtil.writeLog(context, "修改组" + username + password, "changeGroup");
                    }
                }else if ("download".equals(msg.value)) {

                }

                break;
            default:
                Log.w(TAG, "未知消息类型: " + msg.type);
                // 日志
                LocalLogUtil.writeLog(context, "无效指令", "error");
        }
    }
}
