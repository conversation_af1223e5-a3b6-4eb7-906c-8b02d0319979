<?xml version="1.0" encoding="UTF-8"?><!--This is an auto generated document. Changes to this document may cause incorrect behavior.--><wap-provisioningdoc>
    
  <characteristic type="ProfileInfo">
    <parm name="created_wizard_version" value="2.1.14"/>
  </characteristic>
    
  <characteristic type="Profile">
    <parm name="ProfileName" value="OEM-Service"/>
    <parm name="ModifiedDate" value="2025-06-24 17:03:15"/>
    <parm name="TargetSystemVersion" value="9.3"/>
        
    <characteristic type="AccessMgr" version="11.9">
      <parm name="emdk_name" value=""/>
      <parm name="ServiceAccessAction" value="4"/>
      <parm name="ServiceIdentifier" value="content://oem_info/oem.zebra.secure/wifi_mac"/>
      <parm name="CallerPackageName" value="com.symbol.mdm_l"/>
      <parm name="CallerSignature" value="MIIDUjCCAjqgAwIBAgIJAKUJMT012lFLMA0GCSqGSIb3DQEBCwUAMFYxCzAJBgNVBAYTAkNOMQswCQYDVQQIEwJzejELMAkGA1UEBxMCanMxDTALBgNVBAoTBHNwcnQxDTALBgNVBAsTBHNwcnQxDzANBgNVBAMTBmxpZmVuZzAgFw0yNTA2MjUwNzU5MTVaGA8yMDUyMTExMDA3NTkxNVowVjELMAkGA1UEBhMCQ04xCzAJBgNVBAgTAnN6MQswCQYDVQQHEwJqczENMAsGA1UEChMEc3BydDENMAsGA1UECxMEc3BydDEPMA0GA1UEAxMGbGlmZW5nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAwxgkfOuNwvfZfIOOQizUCxhV5IzuqCVIqBi5j4GsnVvrt7WYB80qZb7iTWOrF0RQwjTXGqmIUFSey65XuxecFSS7OAdu2Qoy3m163o70I58/wZqpPe59tiS+OuxBh9/bEgvi8nMEDzTJTbB+Ylao84Loh864G4qubfUldwoJxAlbvWGN5WL/V5QV8D6bHaNYtStfumR4r7NRB72irAPW45L7Q6TbeSn1vOmmyWWdhvWLtbxkck+WNcpXFukYYkYUzm77eYjfQlHnqVUFFhT4/2bkjYuFGWME1L8jjLpsv57yQtf6oP97/7KQPcsVr4PZqdOLXDEdGfoRP22QJU1BzwIDAQABoyEwHzAdBgNVHQ4EFgQUcloOIUQe9MxbPBRP2N/Ouo0VNoEwDQYJKoZIhvcNAQELBQADggEBAKk7NZGRZY5NPkXg6Ie+0GhZO5rtM/dWbN2pDIi3R0pX2Dkimcjx5XAD15a/CmXdW5TPABhsJrkPC8Gi84J8hWDmBoryqeoGbgbPLjxayRYkov1NcE4vGu/+UwaUjGSYCeWKXcMXjY7KocB99H5QxiVAm5/0bLx2QQN5y1v47DC5Kl3YT5BFl+RguH0daQW2WG3gT7Ud37jpD4EwUenxWI3oQewG8iMNydhZ6NA2wjVlb/ES1WQ1ImTuMWa60EnP9WWkKKk5hSq0nY2HqAxCWulTs3YlAP1cLqGf1MDcAKzdQSHCp9O5DCDTrna8486LabGTgUlbPDkRkuev0VLKgmU="/>
    </characteristic>
      
  </characteristic>
  
</wap-provisioningdoc>


<!--    <?xml version="1.0" encoding="UTF-8"?>-->
<!--<wap-provisioningdoc>-->

<!--<characteristic type="ProfileInfo">-->
<!--  <parm name="created_wizard_version" value="2.1.14"/>-->
<!--</characteristic>-->

<!--<characteristic type="Profile">-->
<!--  <parm name="ProfileName" value="OEM-Service"/>-->
<!--  <parm name="ModifiedDate" value="2025-06-25 10:30:00"/>-->
<!--  <parm name="TargetSystemVersion" value="9.3"/>-->

<!--  &lt;!&ndash; 1. 授权 App 访问设备 SN &ndash;&gt;-->
<!--  <characteristic type="AccessMgr" version="11.9">-->
<!--    <parm name="ServiceAccessAction" value="4"/>-->
<!--    <parm name="ServiceIdentifier" value="content://oem_info/oem.zebra.secure/build_serial"/>-->
<!--    <parm name="CallerPackageName" value="com.symbol.mdm_l"/>-->
<!--    <parm name="CallerSignature" value="你的签名Base64"/>-->
<!--  </characteristic>-->

<!--  &lt;!&ndash; 2. 授权 App 访问 WiFi MAC 地址 &ndash;&gt;-->
<!--  <characteristic type="AccessMgr" version="11.9">-->
<!--    <parm name="ServiceAccessAction" value="4"/>-->
<!--    <parm name="ServiceIdentifier" value="content://oem_info/oem.zebra.secure/wifi_mac"/>-->
<!--    <parm name="CallerPackageName" value="com.symbol.mdm_l"/>-->
<!--    <parm name="CallerSignature" value="你的签名Base64"/>-->
<!--  </characteristic>-->

<!--  &lt;!&ndash; ✅ 3. 授权 App 控制蓝牙开关 &ndash;&gt;-->
<!--  <characteristic type="AccessMgr" version="11.9">-->
<!--    <parm name="ServiceAccessAction" value="4"/>-->
<!--    <parm name="ServiceIdentifier" value="content://settings/system/bluetooth_on"/>-->
<!--    <parm name="CallerPackageName" value="com.symbol.mdm_l"/>-->
<!--    <parm name="CallerSignature" value="你的签名Base64"/>-->
<!--  </characteristic>-->

<!--  &lt;!&ndash; ✅ 4. 授权 App 控制 USB 功能（如 USB 调试） &ndash;&gt;-->
<!--  <characteristic type="AccessMgr" version="11.9">-->
<!--    <parm name="ServiceAccessAction" value="4"/>-->
<!--    <parm name="ServiceIdentifier" value="content://settings/global/adb_enabled"/>-->
<!--    <parm name="CallerPackageName" value="com.symbol.mdm_l"/>-->
<!--    <parm name="CallerSignature" value="你的签名Base64"/>-->
<!--  </characteristic>-->

<!--  &lt;!&ndash; ✅ 5. 授权 App 控制 摄像头状态 &ndash;&gt;-->
<!--  <characteristic type="AccessMgr" version="11.9">-->
<!--    <parm name="ServiceAccessAction" value="4"/>-->
<!--    <parm name="ServiceIdentifier" value="content://settings/secure/camera_disabled"/>-->
<!--    <parm name="CallerPackageName" value="com.symbol.mdm_l"/>-->
<!--    <parm name="CallerSignature" value="你的签名Base64"/>-->
<!--  </characteristic>-->

<!--</characteristic>-->
<!--</wap-provisioningdoc>-->
