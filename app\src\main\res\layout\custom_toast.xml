<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_error"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="12dp"
    >

    <ImageView
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="8dp"
        android:src="@android:drawable/ic_dialog_alert" />

    <TextView
        android:id="@+id/toast_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="自定义警告"
        android:textColor="#B71C1C"
        android:textSize="16sp" />
</LinearLayout>
