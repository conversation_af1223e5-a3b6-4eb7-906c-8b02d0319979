plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 29
    buildToolsVersion "29.0.2"
    signingConfigs {
        debug {
            storeFile file('D:\\bigcherry\\android\\my-release-key.jks')
            storePassword '123456'
            keyPassword '123456'
            keyAlias 'mykey'
        }
//        release {
//            storeFile file('D:\\bigcherry\\android\\my-release-key.jks')
//            storePassword '123456'
//            keyAlias 'mykey'
//            keyPassword '123456'
//
//        }
    }
    defaultConfig {
        applicationId "com.example.mdm_l"
        minSdkVersion 28
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'com.google.android.material:material:1.1.0'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    implementation "com.squareup.okhttp3:okhttp:3.14.1"
    implementation 'com.google.code.gson:gson:2.8.9'
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'
    implementation 'com.google.android.gms:play-services-location:21.0.1'
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}