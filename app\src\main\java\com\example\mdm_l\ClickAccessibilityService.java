package com.example.mdm_l;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.GestureDescription;
import android.graphics.Path;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;

public class ClickAccessibilityService extends AccessibilityService {
    public static ClickAccessibilityService instance;

    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        instance = this;
        Log.d("ClickService", "无障碍服务已连接");
    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {}

    @Override
    public void onInterrupt() {}

    // 点击操作
    public void clickPercent(int percentX, int percentY) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            int screenWidth = getResources().getDisplayMetrics().widthPixels;
            int screenHeight = getRealScreenHeight();

            int x = (int) (screenWidth * (percentX / 100.0));
            int y = (int) (screenHeight * (percentY / 100.0));

            Path path = new Path();
            path.moveTo(x, y);

            GestureDescription.StrokeDescription strokeDescription =
                    new GestureDescription.StrokeDescription(path, 0, 100);

            GestureDescription gestureDescription =
                    new GestureDescription.Builder().addStroke(strokeDescription).build();

            dispatchGesture(gestureDescription, new GestureResultCallback() {
                @Override
                public void onCompleted(GestureDescription gestureDescription) {
                    super.onCompleted(gestureDescription);
                    Log.d("ClickService", "点击完成 (" + x + ", " + y + ")");
                }
            }, null);
        } else {
            Log.d("错误", "获取坐标失败");
        }
    }

    // ✅ 添加滑动操作（新增）
    public void swipePercent(int startPercentX, int startPercentY, int endPercentX, int endPercentY, long durationMs) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            new Handler(Looper.getMainLooper()).post(() -> {
                int screenWidth = getResources().getDisplayMetrics().widthPixels;
                int screenHeight = getRealScreenHeight();
                int startX = (int) (screenWidth * (startPercentX / 100.0));
                int startY = (int) (screenHeight * (startPercentY / 100.0));
                int endX = (int) (screenWidth * (endPercentX / 100.0));
                int endY = (int) (screenHeight * (endPercentY / 100.0));

                Log.d("ClickService", "准备滑动: (" + startX + "," + startY + ") -> (" + endX + "," + endY + ")");

                Path path = new Path();
                path.moveTo(startX, startY);
                path.lineTo(endX, endY);

                GestureDescription.StrokeDescription stroke =
                        new GestureDescription.StrokeDescription(path, 0, durationMs);

                GestureDescription gesture =
                        new GestureDescription.Builder().addStroke(stroke).build();

                dispatchGesture(gesture, new GestureResultCallback() {
                    @Override
                    public void onCompleted(GestureDescription gestureDescription) {
                        super.onCompleted(gestureDescription);
                        Log.d("ClickService", "滑动完成: (" + startX + "," + startY + ") -> (" + endX + "," + endY + ")");
                    }

                    @Override
                    public void onCancelled(GestureDescription gestureDescription) {
                        super.onCancelled(gestureDescription);
                        Log.e("ClickService", "滑动失败");
                    }
                }, null);
            });
        }
    }


    private int getRealScreenHeight() {
        try {
            android.view.Display display = ((android.view.WindowManager) getSystemService(WINDOW_SERVICE)).getDefaultDisplay();
            android.util.DisplayMetrics metrics = new android.util.DisplayMetrics();

            Class<?> displayClass = Class.forName("android.view.Display");
            java.lang.reflect.Method method = displayClass.getMethod("getRealMetrics", android.util.DisplayMetrics.class);
            method.invoke(display, metrics);

            return metrics.heightPixels;
        } catch (Exception e) {
            Log.e("ClickService", "获取真实屏幕高度失败: " + e.getMessage());
            return getResources().getDisplayMetrics().heightPixels;
        }
    }
    @Override
    public void onDestroy() {
        super.onDestroy();
        instance = null; // ✅ 避免内存泄漏
    }
}
