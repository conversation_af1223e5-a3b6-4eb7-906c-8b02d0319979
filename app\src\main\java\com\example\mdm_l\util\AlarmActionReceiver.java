package com.example.mdm_l.util;

import android.app.NotificationManager;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class AlarmActionReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.i("AlarmReceiver", "收到广播: " + intent.getAction());

        if ("com.example.mdm_l.STOP_ALARM".equals(intent.getAction())) {
            setSound.stop();
            NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            manager.cancel(2001);
        }
    }

}
