package com.example.mdm_l.util;

import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.util.Log;

import com.example.mdm_l.MyDeviceAdminReceiver;

public class setCamera {

    // 禁用摄像头
    public static void noCamera(Context context) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        ComponentName admin = new ComponentName(context, MyDeviceAdminReceiver.class);
        if (dpm.isAdminActive(admin)) {
            dpm.setCameraDisabled(admin, true);
            Log.i("setCamera", "摄像头已禁用");
        } else {
            Log.e("setCamera", "未激活设备管理员权限");
        }
    }

    // 启用摄像头
    public static void yesCamera(Context context) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        ComponentName admin = new ComponentName(context, MyDeviceAdminReceiver.class);
        if (dpm.isAdminActive(admin)) {
            dpm.setCameraDisabled(admin, false);
            Log.i("setCamera", "摄像头已启用");
        } else {
            Log.e("setCamera", "未激活设备管理员权限");
        }
    }
}
