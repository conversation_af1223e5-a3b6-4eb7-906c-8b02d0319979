//package com.example.mdm_l.util;
//
//import android.content.Context;
//import android.os.Handler;
//import android.os.Looper;
//import android.util.Log;
//
//import com.example.mdm_l.domain.LoginInfo;
//import com.example.mdm_l.log.LocalLogUtil;
//import com.google.gson.Gson;
//
//import org.json.JSONObject;
//
//import java.io.IOException;
//import java.io.OutputStream;
//import java.net.Socket;
//
//public class heartbest {
//    private static final String TAG = "HeartBeat";
//    private final Context context;
//    private final String serverIp = "***********";
//    private final int serverPort = 9902;
//    private Socket socket;
//    private OutputStream outputStream;
//    private final Gson gson = new Gson();
//    private volatile boolean isConnected = false;
//    private String sn;
//
//    private Handler handler;
//    private Runnable heartbeatRunnable;
//
//    public heartbest(Context context) {
//        this.context = context;
//        this.sn = getSN.getSerialNumber(context);
//    }
//
//    public void start() {
//        handler = new Handler(Looper.getMainLooper());
//        heartbeatRunnable = new Runnable() {
//            @Override
//            public void run() {
//                try {
//                    if (!isConnected || socket == null || socket.isClosed() || !socket.isConnected()) {
//                        reconnect();
//                    }
//
//                    if (isConnected && outputStream != null) {
//                        String heartbeatData = generateHeartbeatData();
//                        outputStream.write(heartbeatData.getBytes());
//                        outputStream.flush();
//                        Log.i(TAG, "发送心跳：" + heartbeatData);
//                        LocalLogUtil.writeLog(context, "发送心跳：" + heartbeatData, "heartbest");
//                    }
//                } catch (Exception e) {
//                    Log.e(TAG, "发送心跳失败", e);
//                    LocalLogUtil.writeLog(context, "心跳异常：" + e.getMessage(), "heartbestError");
//                    isConnected = false;
//                    closeSocket();
//                }
//
//                handler.postDelayed(this, 30 * 1000); // 每30秒执行一次
//            }
//        };
//
//        handler.post(heartbeatRunnable); // 启动心跳任务
//    }
//
//    public void stop() {
//        if (handler != null && heartbeatRunnable != null) {
//            handler.removeCallbacks(heartbeatRunnable);
//        }
//        closeSocket();
//    }
//
//    private void reconnect() {
//        closeSocket();
//        try {
//            socket = new Socket(serverIp, serverPort);
//            outputStream = socket.getOutputStream();
//            isConnected = true;
//            Log.i(TAG, "连接服务器成功: " + serverIp + ":" + serverPort);
//            LocalLogUtil.writeLog(context, "连接服务器成功", "heartbestConnectSuccess");
//        } catch (IOException e) {
//            LocalLogUtil.writeLog(context, "连接服务器失败: " + e.getMessage(), "heartbestConnectError");
//            isConnected = false;
//        }
//    }
//
//    private void closeSocket() {
//        try {
//            if (socket != null && !socket.isClosed()) {
//                socket.close();
//            }
//            isConnected = false;
//        } catch (IOException e) {
//            LocalLogUtil.writeLog(context, "关闭 socket 失败: " + e.getMessage(), "heartbestError");
//        }
//    }
//
//    private String generateHeartbeatData() {
//        JSONObject json = new JSONObject();
//        LoginInfo info = setLocalInfo.getInfo(context);
//        try {
//            json.put("username", info.username.isEmpty() ? "未知" : info.username);
//            json.put("password", info.password.isEmpty() ? "未知" : info.password);
//            json.put("mac", getMAC.getMACString(context));
//            json.put("sn", this.sn);
//            json.put("ip", getIp.getIpString(context));
//            json.put("battery", getBattery.getBatteryString(context));
//            json.put("wifiName", getWifi.getWifiNameString(context));
//            json.put("rssi", getWifi.getWifiRssiString(context));
//            json.put("apMac", getWifi.getWifiMacAddress(context));
//            json.put("screenState", getScreenWorkStatus.getStatus(context));
//            json.put("isRecharge", getIsRecharge.getRecharge(context));
//            json.put("allPackage", gson.toJson(getPackage.getInstallAppVersion(context)));
//        } catch (Exception e) {
//            LocalLogUtil.writeLog(context, "生成心跳 JSON 失败: " + e.getMessage(), "heartbestError");
//        }
//        return json.toString();
//    }
//}


package com.example.mdm_l.util;

import android.content.Context;
import android.os.PowerManager;
import android.util.Log;

import com.example.mdm_l.domain.LoginInfo;
import com.example.mdm_l.log.LocalLogUtil;
import com.google.android.gms.dynamic.IFragmentWrapper;
import com.google.gson.Gson;

import org.json.JSONObject;

import java.io.IOException;
import java.io.OutputStream;
import java.net.Socket;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class heartbest {
    private static final String TAG = "HeartBeat";
    private final Context context;
    private final String serverIp = "***********4";
    private final int serverPort = 9902;
    private Socket socket;
    private OutputStream outputStream;
    private final Gson gson = new Gson();
    private ScheduledExecutorService scheduler;
    private volatile boolean isConnected = false;
    private String sn;
    private String mac;
    private PowerManager.WakeLock wakeLock;


    //    public heartbest(Context context, String serverIp, int serverPort, String MAC, String sn) {
    public heartbest(Context context) {
        this.context = context;
//        this.serverIp = serverIp;
//        this.serverPort = serverPort;
//        this.MAC = MAC;
//        this.sn = getSN.getSerialNumber(context);

    }

    public void start() {
        acquireWakeLock();
        scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleAtFixedRate(() -> {
            try {
                if (!isConnected || socket == null || socket.isClosed() || !socket.isConnected()) {
                    reconnect();
                }

                if (isConnected && outputStream != null) {
                    String heartbeatData = generateHeartbeatData();
                    outputStream.write(heartbeatData.getBytes());
                    outputStream.flush();
//                    Log.i(TAG, "发送心跳：" + heartbeatData);
                    LocalLogUtil.writeLog(context, "发送心跳：" + heartbeatData, "heartbeat");
                }
            } catch (Exception e) {
                Log.e(TAG, "发送心跳失败", e);
                LocalLogUtil.writeLog(context, "心跳异常：" + e.getMessage(), "heartbeatError");
                isConnected = false;
                closeSocket();
            }
        }, 0, 60, TimeUnit.SECONDS);
    }

    public void stop() {
        if (scheduler != null) {
            scheduler.shutdownNow();
            scheduler = null;
        }
        closeSocket();
        releaseWakeLock();
    }

    private void reconnect() {
        closeSocket();
        try {
            socket = new Socket(serverIp, serverPort);
            outputStream = socket.getOutputStream();
            isConnected = true;
            Log.i(TAG, "连接服务器成功: " + serverIp + ":" + serverPort);
            LocalLogUtil.writeLog(context, "连接服务器成功", "heartbestConnectSuccess");
        } catch (IOException e) {
            // Log.e(TAG, "重连服务器失败", e);
            LocalLogUtil.writeLog(context, "连接服务器失败" + e.getMessage(), "heartbeatConnectError");
            isConnected = false;
        }
    }

    private void closeSocket() {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.close();
            }
            isConnected = false;
        } catch (IOException e) {
            LocalLogUtil.writeLog(context, "关闭 socket 失败" + e.getMessage(), "heartbeatError");
        }
    }

    private String generateHeartbeatData() {
        JSONObject json = new JSONObject();
        LoginInfo info = setLocalInfo.getInfo(context);
        try {
            json.put("username", info.username.isEmpty() ? "未知" : info.username); // 每次发送心跳获取最新值
            json.put("password", info.password.isEmpty() ? "未知" : info.password); // 每次发送心跳获取最新值
            json.put("sn", info.sn.isEmpty() ? "未知" : info.sn); // 每次发送心跳获取最新值
            json.put("mac", info.mac.isEmpty() ? "未知" : info.mac); // 每次发送心跳获取最新值
//            json.put("mac", getMAC.getMACString(context));
            json.put("ip", getIp.getIpString(context));
            json.put("battery", getBattery.getBatteryString(context));
            json.put("wifiName", getWifi.getWifiNameString(context));
            json.put("rssi", getWifi.getWifiRssiString(context));
            json.put("apMac", getWifi.getWifiMacAddress(context));
            json.put("screenState", getScreenWorkStatus.getStatus(context));
            json.put("isRecharge", getIsRecharge.getRecharge(context));
            json.put("allPackage", gson.toJson(getPackage.getInstallAppVersion(context)));
            json.put("deviceName", DeviceNameUtil.getDeviceName(context).isEmpty() ? "未知" : DeviceNameUtil.getDeviceName(context));
            System.out.println(json);
        } catch (Exception e) {
            LocalLogUtil.writeLog(context, "生成心跳 JSON 失败" + e.getMessage(), "heartbeatError");
        }
        return json.toString();
    }

    // 防止cpu休眠
    private void acquireWakeLock() {
        if (wakeLock == null) {
            PowerManager pm = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
            wakeLock = pm.newWakeLock(PowerManager.PARTIAL_WAKE_LOCK, "Heartbeat::Wakelock");
            wakeLock.setReferenceCounted(false);
            wakeLock.acquire();
        }
    }

    private void releaseWakeLock() {
        if (wakeLock != null && wakeLock.isHeld()) {
            wakeLock.release();
        }
    }
}

