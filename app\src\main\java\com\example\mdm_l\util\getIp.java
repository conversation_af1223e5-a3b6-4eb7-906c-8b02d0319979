package com.example.mdm_l.util;

import android.content.Context;
import android.net.wifi.WifiInfo;
import android.net.wifi.WifiManager;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

public class getIp {
    public static String getIpString(Context context) {
        String ip = getWifiIp(context);
        if (!ip.equals("0.0.0.0") && !ip.equals("未知")) {
            return ip;
        }

        // 如果 Wi-Fi IP 获取不到，尝试获取其他接口 IP
        return getLocalIp();
    }
    // 从 WifiManager 获取 IP（需要连接 WiFi）
    private static String getWifiIp(Context context) {
        try {
            WifiManager wifiManager = (WifiManager) context.getApplicationContext().getSystemService(Context.WIFI_SERVICE);
            if (wifiManager != null) {
                WifiInfo wifiInfo = wifiManager.getConnectionInfo();
                int ipInt = wifiInfo.getIpAddress();
                return (ipInt & 0xFF) + "." +
                        ((ipInt >> 8) & 0xFF) + "." +
                        ((ipInt >> 16) & 0xFF) + "." +
                        ((ipInt >> 24) & 0xFF);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "未知";
    }
    // 从 NetworkInterface 获取本地 IP（适用于有线/热点等）
    private static String getLocalIp() {
        try {
            List<NetworkInterface> interfaces = Collections.list(NetworkInterface.getNetworkInterfaces());
            for (NetworkInterface nif : interfaces) {
                Enumeration<InetAddress> addresses = nif.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress inetAddress = addresses.nextElement();
                    if (!inetAddress.isLoopbackAddress() &&
                            inetAddress instanceof java.net.Inet4Address) {
                        return inetAddress.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "未知";
    }
}
