package com.example.mdm_l.util;

import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.provider.Settings;
import android.util.Log;

import com.example.mdm_l.MyDeviceAdminReceiver;

public class DeviceNameUtil {

    private static final String TAG = "DeviceNameUtil";

    // 获取设备名称（优先读取 Settings.Global.DEVICE_NAME，如果失败则读 Settings.Secure.BLUETOOTH_NAME）
    public static String getDeviceName(Context context) {
        String deviceName = null;
        try {
            deviceName = Settings.Global.getString(context.getContentResolver(), "device_name");
        } catch (Exception e) {
            Log.e(TAG, "获取设备名称失败: " + e.getMessage());
        }
        return deviceName != null ? deviceName : android.os.Build.MODEL;
    }

    // 设置设备名称，必须是设备所有者  不能使用
    public static boolean setDeviceName(Context context, String newName) {
        boolean success = false;
        try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
            ComponentName adminComponent = new ComponentName(context, MyDeviceAdminReceiver.class);
            if (dpm != null && dpm.isDeviceOwnerApp(context.getPackageName())) {
                dpm.setGlobalSetting(adminComponent, Settings.Global.DEVICE_NAME, newName);
                success = true; // 只要没有抛异常，我们认为设置成功
                if (success) {
                    Log.d(TAG, "设备名称设置成功: " + newName);
                } else {
                    Log.e(TAG, "设备名称设置失败，setGlobalSetting返回false");
                }
            } else {
                Log.e(TAG, "当前应用不是设备所有者，无法设置设备名称");
            }
        } catch (Exception e) {
            Log.e(TAG, "设置设备名称异常: " + e.getMessage());
        }
        return success;
    }
}
