package com.example.mdm_l;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.ComponentName;
import android.content.Intent;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.core.app.NotificationCompat;

import com.example.mdm_l.util.WebSocketHandler;
import com.example.mdm_l.util.webSocketServer;

public class BootStartService extends Service {

    private static final String CHANNEL_ID = "boot_start_channel";

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
        Notification notification = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("设备初始化")
                .setContentText("正在启动服务...")
                .setSmallIcon(R.mipmap.ic_launcher)
                .build();
        startForeground(1, notification);

        Log.i("BootStartService", "服务已启动");

        // 启动你的主页面（可选）
        Intent activityIntent = new Intent(this, login.class);
        activityIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        startActivity(activityIntent);
//        // 启动 HeartbeestService（心跳服务）
//        Intent hbIntent = new Intent(this, HeartbeatService.class);
//        startForegroundService(hbIntent);
        // // 优化页面中 暂时关闭
        //        ScreenshotUtil.requestScreenshotPermission(this);



        // 启动 WebSocketService
//        Intent wsIntent = new Intent(this, WebSocketService.class);
//        startForegroundService(wsIntent);
        // 任务完成后可停止自身
        stopSelf();
    }

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID, "Boot Start Channel", NotificationManager.IMPORTANCE_LOW);
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) manager.createNotificationChannel(channel);
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
