package com.example.mdm_l.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.util.Log;
// 暂时弃用
public class getSN {
    public static String getSerialNumber(Context context) {
        String serial = null;
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ 必须获取权限
                if (context.checkSelfPermission(Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
                    serial = Build.getSerial(); // Android 10+ 只有设备所有者才可访问
                    System.out.println(serial);
                } else {
                    Log.e("DeviceUtils", "缺少 READ_PHONE_STATE 权限");
                }
            } else {
                // Android 7.x 及以下直接可用
                serial = Build.SERIAL;
            }
        } catch (Exception e) {
            Log.e("DeviceUtils", "获取 SN 失败: " + e.getMessage());
        }
        return serial;
    }
}
