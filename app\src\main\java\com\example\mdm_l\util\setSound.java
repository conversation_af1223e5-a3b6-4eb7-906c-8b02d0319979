package com.example.mdm_l.util;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.media.MediaPlayer;
import android.os.Build;

import androidx.core.app.NotificationCompat;

import com.example.mdm_l.R;

public class setSound {
    private static MediaPlayer mediaPlayer;
    private static boolean isPaused = false;

    // 播放 res/raw 中的 bg.mp3
    public static void play(Context context) {
        if (mediaPlayer == null) {
            mediaPlayer = MediaPlayer.create(context, R.raw.bg); // 注意 bg 为 res/raw/bg.mp3
            if (mediaPlayer != null) {
                mediaPlayer.setLooping(true); // 设置循环播放
                mediaPlayer.start();
                isPaused = false;


                NotificationManager notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                String channelId = "alarm_channel";

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    NotificationChannel channel = new NotificationChannel(channelId, "报警通知", NotificationManager.IMPORTANCE_HIGH);
                    notificationManager.createNotificationChannel(channel);
                }

                Intent stopIntent = new Intent(context, AlarmActionReceiver.class);
                stopIntent.setAction("com.example.mdm_l.STOP_ALARM");
                PendingIntent stopPendingIntent = PendingIntent.getBroadcast(context, 0, stopIntent, PendingIntent.FLAG_IMMUTABLE);


                Notification notification = new NotificationCompat.Builder(context, channelId)
                        .setContentTitle("设备报警中 点击停止报警")
                        .setSmallIcon(R.drawable.spcode2)
                        .addAction(new NotificationCompat.Action(0, "停止报警", stopPendingIntent))
                        .setOngoing(true) // 防止滑动清除
                        .build();
                notificationManager.notify(2001, notification);


            }
        } else if (isPaused) {
            mediaPlayer.start();
            isPaused = false;
        }
    }

    // 暂停播放
    public static void pause() {
        if (mediaPlayer != null && mediaPlayer.isPlaying()) {
            mediaPlayer.pause();
            isPaused = true;
        }
    }

    // 停止并释放资源
    public static void stop() {
        if (mediaPlayer != null) {
            if (mediaPlayer.isPlaying() || isPaused) {
                mediaPlayer.stop();
            }
            mediaPlayer.release();
            mediaPlayer = null;
            isPaused = false;
        }
    }

    // 是否正在播放
    public static boolean isPlaying() {
        return mediaPlayer != null && mediaPlayer.isPlaying();
    }

    // 是否已暂停
    public static boolean isPaused() {
        return isPaused;
    }
}
