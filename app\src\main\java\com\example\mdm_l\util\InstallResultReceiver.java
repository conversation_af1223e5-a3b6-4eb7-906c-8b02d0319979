package com.example.mdm_l.util;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInstaller;
import android.content.pm.PackageManager;
import android.util.Log;

public class InstallResultReceiver extends BroadcastReceiver {
    @Override
    public void onReceive(Context context, Intent intent) {
        int status = intent.getIntExtra(PackageInstaller.EXTRA_STATUS, -1);
        String message = intent.getStringExtra(PackageInstaller.EXTRA_STATUS_MESSAGE);
        String pkgName = intent.getStringExtra(PackageInstaller.EXTRA_PACKAGE_NAME);
        boolean isUninstall = intent.getBooleanExtra("uninstall", false);

        if (status == PackageInstaller.STATUS_SUCCESS) {
            Log.i(isUninstall ? "静默卸载" : "静默安装", "成功" + pkgName);
        } else {
            // fallback 检查是否成功
            if (!isUninstall && isPackageInstalled(context, pkgName)) {
                Log.w("静默安装", "状态码为-1，但目标应用已安装: " + pkgName);
            } else if (isUninstall && !isPackageInstalled(context, pkgName)) {
                Log.w("静默卸载", "状态码为-1，但目标应用已卸载: " + pkgName);
            } else {
                Log.e(isUninstall ? "静默卸载" : "静默安装",
                        "失败 status=" + status + " message=" + message);
            }
        }
    }

    private boolean isPackageInstalled(Context context, String packageName) {
        if (packageName == null) return false;
        try {
            context.getPackageManager().getPackageInfo(packageName, 0);
            return true;
        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }
    }
}



