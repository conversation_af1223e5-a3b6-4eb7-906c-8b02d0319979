package com.example.mdm_l.util;

import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.util.Log;

import com.example.mdm_l.login; // login 是你的 DeviceAdminReceiver

public class setShutdown {

    private static final String TAG = "SetShutdown";

    public static void shutdown(Context context,ComponentName adminComponent) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        if (dpm.isAdminActive(adminComponent)) {
            dpm.reboot(adminComponent); // 或 shutdown(adminComponent) 具体看版本
            Log.d("SetShutdown", "设备管理器已激活，准备关机");
        } else {
            Log.e("SetShutdown", "当前没有激活的设备管理器，无法关机！");
        }
    }
}
