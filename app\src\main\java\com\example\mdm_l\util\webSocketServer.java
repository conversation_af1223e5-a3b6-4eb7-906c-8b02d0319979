package com.example.mdm_l.util;

import android.util.Log;

import com.example.mdm_l.log.LocalLogUtil;

import org.java_websocket.WebSocket;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.server.WebSocketServer;

import java.net.InetSocketAddress;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

public class webSocketServer extends WebSocketServer {

    private static final String TAG = "WebSocketServer";
    private static webSocketServer instance;
    private static final int PORT = 8080;
    private boolean started = false;
    private final Set<WebSocket> connections = Collections.synchronizedSet(new HashSet<>());
    private WebSocket lastConnectedClient = null; // 最新连接用户
    public interface ServerListener {
        void onClientMessage(String message);
        void onClientConnected(String ip);
        void onClientDisconnected(String ip);
    }

    private ServerListener listener;

    public static synchronized webSocketServer getInstance() {
        if (instance == null) {
            instance = new webSocketServer();
        }
        return instance;
    }

    private webSocketServer() {
        super(new InetSocketAddress(PORT));
    }

    public void setListener(ServerListener listener) {
        this.listener = listener;
    }

    @Override
    public void onOpen(WebSocket conn, ClientHandshake handshake) {
        connections.add(conn);
        lastConnectedClient = conn;
        Log.d(TAG, "客户端已连接：" + conn.getRemoteSocketAddress());
        if (listener != null) {
            listener.onClientConnected(conn.getRemoteSocketAddress().toString());
        }
    }

    @Override
    public void onClose(WebSocket conn, int code, String reason, boolean remote) {
        connections.remove(conn);
        ScreenshotUtil.stopContinuousCapture();
        if (listener != null) {
            listener.onClientDisconnected(conn.getRemoteSocketAddress().toString());
        }
    }

    @Override
    public void onMessage(WebSocket conn, String message) {
        if (listener != null) {
            listener.onClientMessage(message);
        }
    }

    @Override
    public void onError(WebSocket conn, Exception ex) {
        Log.e(TAG, "发生错误：" + ex.getMessage());
    }

    @Override
    public void onStart() {
        started = true;
        Log.d(TAG, "WebSocket 服务启动，端口：" + PORT);
//        // 日志
//        LocalLogUtil.writeLog(getApplicationContext(), "登录日志","loginError");
    }

    // 新增启动方法，避免重复启动异常
    public synchronized void startServer() {
        if (!started) {
            try {
                start();
            } catch (IllegalStateException e) {
                Log.w(TAG, "WebSocket服务已经启动，无需重复启动");
            } catch (Exception e) {
                Log.e(TAG, "启动WebSocket服务失败", e);
            }
        } else {
            Log.d(TAG, "WebSocket服务已启动，无需重复启动");
        }
    }

    // 发送消息给所有连接的客户端
    public void sendToAll(String message) {
        for (WebSocket conn : connections) {
            Log.d(TAG, "" + conn.getRemoteSocketAddress());
            conn.send(message);
        }
    }
    // 发送消息给当前用户
    public void sendToActiveUser(String message) {
        if(lastConnectedClient != null && lastConnectedClient.isOpen()) {
            lastConnectedClient.send(message);
            Log.i(TAG, "已发送给当前用户：" + lastConnectedClient.getRemoteSocketAddress());
        }else {
            Log.w(TAG, "当前没有用户连接");
        }
    }

    public synchronized void stopServer() {
        try {
            stop();
            connections.clear(); // 清理连接池
        } catch (Exception e) {
            e.printStackTrace();
        }
        instance = null;
        started = false;
    }

    public boolean isStarted() {
        return started;
    }
}

