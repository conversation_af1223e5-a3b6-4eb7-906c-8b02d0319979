package com.example.mdm_l.util;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.example.mdm_l.domain.LoginInfo;

public class setLocalInfo {
    public static void setInfo(Context context, LoginInfo info) {
        // 保存账号密码
        SharedPreferences sharedPreferences = context.getSharedPreferences("login_info", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        // 读取原数据
        LoginInfo oldInfo = getInfo(context);
        // 只更新非空字段
        editor.putString("username", info.username != null ? info.username : oldInfo.username);
        editor.putString("password", info.password != null ? info.password : oldInfo.password);
        editor.putString("sn",       info.sn       != null ? info.sn       : oldInfo.sn);
        editor.putString("mac",      info.mac      != null ? info.mac      : oldInfo.mac);
        editor.apply();
    }
    public static LoginInfo getInfo(Context context) {
        SharedPreferences sp = context.getSharedPreferences("login_info", Context.MODE_PRIVATE);
        String username = sp.getString("username", "");
        String password = sp.getString("password", "");
        String sn = sp.getString("sn", "");
        String mac = sp.getString("mac", "");
        LoginInfo info = new LoginInfo();
        info.username = username != "" ? username : null;
        info.password = password != "" ? password : null;
        info.sn = sn != "" ? sn : null;
        info.mac = mac != "" ? mac : null;
        Log.d("初次", username + password + sn + mac);
        return info;
    }
}
