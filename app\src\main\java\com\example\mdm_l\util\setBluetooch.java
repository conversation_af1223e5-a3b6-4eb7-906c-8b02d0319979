package com.example.mdm_l.util;

import android.app.admin.DevicePolicyManager;
import android.bluetooth.BluetoothAdapter;
import android.content.ComponentName;
import android.content.Context;
import android.util.Log;

import com.example.mdm_l.MyDeviceAdminReceiver;

public class setBluetooch {

    // 禁用蓝牙（彻底禁用蓝牙开关）
    public static void disableBluetooth(Context context) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        ComponentName adminComponent = new ComponentName(context, MyDeviceAdminReceiver.class);

        if (dpm.isAdminActive(adminComponent)) {
            dpm.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_BLUETOOTH);
            dpm.addUserRestriction(adminComponent, android.os.UserManager.DISALLOW_BLUETOOTH_SHARING);
            Log.i("setBluetooch", "蓝牙功能已被彻底禁用，用户无法手动打开");

            // 同时关闭蓝牙
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null && bluetoothAdapter.isEnabled()) {
                bluetoothAdapter.disable();
            }
        } else {
            Log.e("setBluetooch", "设备管理员未激活，无法禁用蓝牙");
        }
    }

    // 启用蓝牙（解除限制，允许用户使用蓝牙）
    public static void enableBluetooth(Context context) {
        DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(Context.DEVICE_POLICY_SERVICE);
        ComponentName adminComponent = new ComponentName(context, MyDeviceAdminReceiver.class);

        if (dpm.isAdminActive(adminComponent)) {
            dpm.clearUserRestriction(adminComponent, android.os.UserManager.DISALLOW_BLUETOOTH);
            dpm.clearUserRestriction(adminComponent, android.os.UserManager.DISALLOW_BLUETOOTH_SHARING);
            Log.i("setBluetooch", "蓝牙功能已解除禁用");

            // 可选：立即打开蓝牙
            BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
            if (bluetoothAdapter != null && !bluetoothAdapter.isEnabled()) {
                bluetoothAdapter.enable();
            }
        } else {
            Log.e("setBluetooch", "设备管理员未激活，无法启用蓝牙");
        }
    }
}
