<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.example.mdm_l">
    <!--  网络 wifi 权限   访问和修改 Wi-Fi 状态，扫描设备周围网络 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- ip -->
    <uses-permission android:name="android.permission.DELETE_PACKAGES" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.INTERNET" /> <!-- mac -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <!--  存储权限  访问/写入外部存储 -->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" android:maxSdkVersion="33" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!--  电源操作权限  -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SHUTDOWN" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REBOOT" tools:ignore="ProtectedPermissions" />
    <!--  设备管理权限 MDM  -->
    <uses-permission android:name="android.permission.MANAGE_USERS" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_ADMINS" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!--  蓝牙  -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <!--  摄像头  -->
    <uses-permission android:name="android.permission.MANAGE_CAMERA" tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.MANAGE_DEVICE_ADMINS" tools:ignore="ProtectedPermissions" />

    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <!--    <uses-permission android:name="android.permission.INSTALL_PACKAGES" />-->
    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>
    <uses-permission android:name="com.zebra.provider.READ"/>
    <uses-permission android:name="com.zebra.provider.WRITE"/>
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>
    <uses-permission android:name="com.zebra.provider.READ"/>
    <uses-permission android:name="com.symbol.emdk.permission.EMDK"/>
    <!--  悬浮窗  -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>

    <!-- Android 11 及以下 -->
    <uses-permission android:name="android.permission.BLUETOOTH"/>
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>

    <!-- Android 12+（Android 12 = API 31） -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT"/>

    <queries>
        <provider android:authorities="oem_info" />
        <package android:name="com.symbol.emdk.emdkservice" />
        <package android:name="com.zebra.zebracontentprovider"/>
    </queries>

    <!--  network_security_config 控制明文HTTP  -->
    <application
        android:allowBackup="true"
        android:icon="@mipmap/ic_logo"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_logo_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MDM_l">
        <uses-library android:name="com.symbol.emdk" />
        <meta-data android:name="com.google.android.actions" android:resource="@xml/device_admin_receiver" />
        <!-- 主界面  APP启动入口页面  -->
        <activity android:name=".login">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- 自定义广播接收器  用于接收安装结果的广播通知 -->
        <receiver android:name=".util.InstallResultReceiver" android:exported="true" />
        <!--  自定义广播接收器  开机自启  -->
        <receiver android:name=".BootBroadcastReceiver" android:enabled="true" android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" /> <!-- 某些设备需要 -->
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>
        <!-- 注册设备管理员 -->
        <receiver android:name=".MyDeviceAdminReceiver" android:permission="android.permission.BIND_DEVICE_ADMIN">
            <meta-data
                android:name="android.app.device_admin"
                android:resource="@xml/device_admin_receiver" />
            <intent-filter>
                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
            </intent-filter>
        </receiver>
        <!--    关闭声音通知栏    -->
        <receiver android:name=".util.AlarmActionReceiver" android:exported="true">
            <intent-filter>
                <action android:name="com.example.mdm_l.STOP_ALARM"/>
            </intent-filter>
        </receiver>
        <!--  前台服务  -->
        <service android:name=".BootStartService" android:exported="false" android:foregroundServiceType="dataSync" />
        <!-- webSocket前台服务 -->
        <service android:name=".WebSocketService" android:exported="false" android:foregroundServiceType="dataSync" />
        <!-- 截图前台服务 -->
        <service android:name=".ScreenshotService" android:exported="false" android:foregroundServiceType="mediaProjection" />
        <!-- 辅助功能服务  自动点击 -->
        <service android:name=".ClickAccessibilityService" android:exported="true" android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE">
            <intent-filter>
                <action android:name="android.accessibilityservice.AccessibilityService" />
            </intent-filter>
            <meta-data android:name="android.accessibilityservice" android:resource="@xml/accessibility_service_config" />
        </service>
        <!--  心跳服务  -->
        <service android:name=".HeartbeatService" android:exported="false" android:foregroundServiceType="connectedDevice" />
    </application>
</manifest>