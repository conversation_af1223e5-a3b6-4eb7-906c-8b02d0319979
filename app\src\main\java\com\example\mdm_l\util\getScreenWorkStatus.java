package com.example.mdm_l.util;

import android.content.Context;
import android.os.PowerManager;

public class getScreenWorkStatus {
    public static String getStatus(Context context) {
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        if (powerManager != null) {
            return String.valueOf(powerManager.isScreenOn());
        }
        return "未知";
    }
}
